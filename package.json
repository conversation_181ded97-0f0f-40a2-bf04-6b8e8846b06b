{"name": "talknician-extension", "displayName": "Talknician extension", "version": "1.1.2", "description": "Create and share knowledge guides with screen captures, recordings, and interactive tutorials for easy team collaboration.", "author": "Talknician <<EMAIL>>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@plasmohq/storage": "^1.15.0", "@types/uuid": "^10.0.0", "plasmo": "0.89.4", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "^6.1.13", "uuid": "^11.0.5"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prettier": "3.2.4", "tailwindcss": "^3.4.17", "typescript": "5.3.3"}, "manifest": {"name": "<PERSON><PERSON><PERSON>", "description": "Create and share knowledge guides with screen captures, recordings, and interactive tutorials for easy team collaboration.", "version": "1.1.2", "manifest_version": 3, "author": "<EMAIL>", "host_permissions": ["<all_urls>"], "permissions": ["activeTab", "scripting", "storage", "tabs", "contextMenus", "sidePanel", "desktopCapture"], "background": {"service_worker": "background/screen-recorder.js"}, "side_panel": {"default_path": "tabs/pin-details.html", "guide_path": "tabs/sidepanel.html"}, "externally_connectable": {"matches": ["*://*.talknician.com/*"]}, "web_accessible_resources": [{"resources": ["src/toolbar/*", "assets/icon.png", "assets/video-placeholder.svg"], "matches": ["<all_urls>"]}], "privacy_practices": {"data_purpose": "This extension helps users create and share knowledge guides by capturing screenshots, recording user interactions, and creating interactive tutorials. All data collection is user-initiated and used solely for creating and sharing guides within your organization.", "remote_code_reason": "Remote code is used to securely authenticate users and sync captured knowledge guides with the Talknician server. This enables secure storage and sharing of guides within your organization.", "data_collection": "The extension only collects data when explicitly initiated by the user through the toolbar or context menu actions. No automatic or background data collection occurs.", "data_usage": "Captured data is used solely for creating knowledge guides and is stored securely on Talknician servers. Data is only accessible to authorized users within your organization.", "data_protection": "All data transmission is encrypted using HTTPS. Authentication tokens are stored securely using Chrome's storage API.", "data_compliance": "We comply with Chrome Web Store's Developer Program Policies regarding data collection, user privacy, and security."}, "permissions_justification": {"activeTab": "Required to capture screenshots of the current tab when users create knowledge guides. This permission is only used when users explicitly initiate a capture action.", "scripting": "Needed to inject the toolbar interface and capture tools into web pages. This enables the core functionality of creating interactive guides by recording user actions.", "storage": "Used to securely store user preferences, authentication tokens, and temporary capture data locally. This ensures persistence of user settings and secure authentication.", "tabs": "Required to manage knowledge capture across different tabs and to synchronize the toolbar state. This enables seamless guide creation across multiple pages.", "contextMenus": "Provides convenient right-click menu options for quick access to knowledge capture features. This improves user experience by offering multiple ways to access features.", "sidePanel": "Powers the side panel interface where users can view and manage their captured knowledge guides. This provides a dedicated space for guide management.", "server_side": "Required to securely store and share knowledge guides within your organization. All server communication is encrypted and authenticated."}, "contact_info": {"email": "<EMAIL>", "website": "https://talknician.com", "support": "https://talknician.com/support"}}}