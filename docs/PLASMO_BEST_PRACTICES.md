# Plasmo Framework Best Practices Guide

## Table of Contents

1. [Project Structure](#project-structure)
2. [Content Scripts](#content-scripts)
3. [Messaging System](#messaging-system)
4. [Storage Management](#storage-management)
5. [UI Components & Styling](#ui-components--styling)
6. [Background Service Worker](#background-service-worker)
7. [Extension Pages](#extension-pages)
8. [Development Workflow](#development-workflow)

## Project Structure

### Recommended Directory Layout

```
src/
├── background/              # Background service worker
│   ├── index.ts            # Main background script
│   └── messages/           # Message handlers
│       ├── ping.ts
│       └── upload.ts
├── components/             # Reusable UI components
│   ├── common/            # Shared components
│   ├── recording/         # Recording-specific components
│   └── toolbar/           # Toolbar components
├── contents/              # Content scripts
│   ├── content.tsx        # Main content script
│   └── overlay.tsx        # Overlay UI
├── hooks/                 # Custom React hooks
├── services/              # Business logic services
├── storage/               # Storage utilities
├── types/                 # TypeScript definitions
├── utils/                 # Utility functions
├── popup.tsx              # Extension popup
├── options.tsx            # Options page
└── sidepanel.tsx          # Side panel
```

### File Naming Conventions

- Use kebab-case for directories: `recording-controls/`
- Use PascalCase for React components: `RecordingTimer.tsx`
- Use camelCase for utilities and services: `storageUtils.ts`
- Use lowercase for content scripts: `content.tsx`

## Content Scripts

### Basic Content Script Setup

```typescript
import type { PlasmoCSConfig } from "plasmo";

export const config: PlasmoCSConfig = {
  matches: ["<all_urls>"],
  all_frames: true,
  world: "ISOLATED", // Default, use "MAIN" for window object access
};

export {};
console.log("Content script loaded");
```

### Content Script UI (CSUI) Best Practices

```typescript
// contents/overlay.tsx
import type { PlasmoGetStyle } from "plasmo"
import cssText from "data-text:~style.css"

export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style")
  style.textContent = cssText
  return style
}

const PlasmoOverlay = () => {
  return (
    <div className="fixed top-4 right-4 z-50 bg-white p-4 rounded shadow-lg">
      <h3>Extension Overlay</h3>
    </div>
  )
}

export default PlasmoOverlay
```

### Inline Content Script UI

```typescript
import type { PlasmoGetInlineAnchor } from "plasmo";

export const getInlineAnchor: PlasmoGetInlineAnchor = async () => ({
  element: document.querySelector("#target-element"),
  insertPosition: "afterend",
});
```

## Messaging System

### Background Message Handler

```typescript
// background/messages/ping.ts
import type { PlasmoMessaging } from "@plasmohq/messaging";

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  console.log("Received:", req.body);

  res.send({
    message: "pong",
    timestamp: Date.now(),
  });
};

export default handler;
```

### Sending Messages from Content Script

```typescript
import { sendToBackground } from "@plasmohq/messaging";

const response = await sendToBackground({
  name: "ping",
  body: {
    data: "Hello from content script",
  },
});
```

### Port-based Communication

```typescript
// background/ports/stream.ts
import type { PlasmoMessaging } from "@plasmohq/messaging";

const handler: PlasmoMessaging.PortHandler = async (req, res) => {
  // Handle streaming data
  res.send({ status: "connected" });
};

export default handler;
```

### Message Relay for Web Pages

```typescript
// contents/relay.tsx
import { relayMessage } from "@plasmohq/messaging";
import type { PlasmoCSConfig } from "plasmo";

export const config: PlasmoCSConfig = {
  matches: ["https://example.com/*"],
};

relayMessage({
  name: "ping",
});
```

## Storage Management

### Basic Storage Usage

```typescript
import { Storage } from "@plasmohq/storage";

const storage = new Storage({
  area: "local", // or "sync", "session", "managed"
});

// Set data
await storage.set("key", "value");
await storage.set("user", { name: "John", id: 123 });

// Get data
const value = await storage.get("key");
const user = await storage.get("user");
```

### React Hook Integration

```typescript
import { useStorage } from "@plasmohq/storage/hook"

function MyComponent() {
  const [count, setCount] = useStorage("count", 0)
  const [settings, setSettings] = useStorage("settings", {
    theme: "dark",
    notifications: true
  })

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  )
}
```

### Advanced Storage with Initialization

```typescript
const [value, setValue] = useStorage("key", (stored) =>
  stored === undefined ? "default" : stored
);
```

### Storage Watching

```typescript
storage.watch({
  "user-settings": (change) => {
    console.log("Settings changed:", change.newValue);
  },
});
```

### Secure Storage

```typescript
import { SecureStorage } from "@plasmohq/storage/secure";

const secureStorage = new SecureStorage();
await secureStorage.setPassword("my-secret-key");

await secureStorage.set("sensitive-data", { token: "abc123" });
const data = await secureStorage.get("sensitive-data");
```

## UI Components & Styling

### Tailwind CSS Integration

```typescript
// contents/styled-overlay.tsx
import cssText from "data-text:~style.css"
import type { PlasmoGetStyle } from "plasmo"

export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style")
  style.textContent = cssText
  return style
}

const StyledOverlay = () => {
  return (
    <div className="fixed bottom-4 left-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg">
      <h3 className="text-lg font-bold">Extension UI</h3>
      <button className="mt-2 px-4 py-2 bg-white text-blue-600 rounded hover:bg-gray-100">
        Action
      </button>
    </div>
  )
}

export default StyledOverlay
```

### CSS Modules Support

```typescript
import styleText from "data-text:./style.module.css"
import * as style from "./style.module.css"

export const getStyle = () => {
  const styleElement = document.createElement("style")
  styleElement.textContent = styleText
  return styleElement
}

const ModularComponent = () => (
  <div className={style.container}>
    <h1 className={style.header}>Styled Component</h1>
  </div>
)
```

### Shadow DOM Styling

```css
/* Target Plasmo's shadow container */
#plasmo-shadow-container {
  z-index: 2147483647 !important;
}

#plasmo-inline {
  background: transparent;
}
```

## Background Service Worker

### Basic Background Script

```typescript
// background/index.ts
export {};

console.log("Background service worker started");

// Extension installation handler
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === "install") {
    console.log("Extension installed");
  }
});

// Tab update listener
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete") {
    console.log("Tab loaded:", tab.url);
  }
});
```

### Chrome API Integration

```typescript
// Capture screenshot
const captureTab = async (): Promise<string> => {
  return new Promise((resolve) => {
    chrome.tabs.captureVisibleTab({ format: "png", quality: 90 }, (dataUrl) =>
      resolve(dataUrl)
    );
  });
};

// Inject content script
const injectScript = async (tabId: number) => {
  await chrome.scripting.executeScript({
    target: { tabId },
    files: ["content-script.js"],
  });
};
```

## Extension Pages

### Popup Page

```typescript
// popup.tsx
import { useStorage } from "@plasmohq/storage/hook"

function IndexPopup() {
  const [count, setCount] = useStorage("click-count", 0)

  return (
    <div className="w-80 p-4">
      <h1 className="text-xl font-bold mb-4">Extension Popup</h1>
      <p>Clicks: {count}</p>
      <button
        onClick={() => setCount(count + 1)}
        className="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Click me
      </button>
    </div>
  )
}

export default IndexPopup
```

### Options Page

```typescript
// options.tsx
import { useStorage } from "@plasmohq/storage/hook"

function OptionsPage() {
  const [settings, setSettings] = useStorage("settings", {
    notifications: true,
    theme: "light"
  })

  return (
    <div className="max-w-2xl mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Extension Settings</h1>

      <div className="space-y-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.notifications}
            onChange={(e) => setSettings({
              ...settings,
              notifications: e.target.checked
            })}
          />
          <span className="ml-2">Enable notifications</span>
        </label>

        <select
          value={settings.theme}
          onChange={(e) => setSettings({
            ...settings,
            theme: e.target.value
          })}
          className="border rounded px-3 py-2"
        >
          <option value="light">Light</option>
          <option value="dark">Dark</option>
        </select>
      </div>
    </div>
  )
}

export default OptionsPage
```

### Side Panel

```typescript
// sidepanel.tsx
function SidePanel() {
  return (
    <div className="h-full p-4 bg-gray-50">
      <h2 className="text-lg font-semibold mb-4">Extension Panel</h2>
      <div className="space-y-2">
        <button className="w-full p-2 bg-blue-500 text-white rounded">
          Action 1
        </button>
        <button className="w-full p-2 bg-green-500 text-white rounded">
          Action 2
        </button>
      </div>
    </div>
  )
}

export default SidePanel
```

## Development Workflow

### Environment Setup

```bash
# Install dependencies
pnpm install

# Development mode
pnpm dev

# Production build
pnpm build

# Package for distribution
pnpm package
```

### Environment Variables

```ini
# .env.development
PLASMO_PUBLIC_API_URL=http://localhost:3000
PLASMO_PUBLIC_DEBUG=true

# .env.production
PLASMO_PUBLIC_API_URL=https://api.example.com
PLASMO_PUBLIC_DEBUG=false
```

### Manifest Configuration

```json
// package.json
{
  "manifest": {
    "permissions": ["activeTab", "storage", "scripting"],
    "host_permissions": ["https://*/*"],
    "web_accessible_resources": [
      {
        "resources": ["assets/*"],
        "matches": ["<all_urls>"]
      }
    ]
  }
}
```

### TypeScript Configuration

```typescript
// types/global.d.ts
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    dataLayer?: any[];
  }
}

export {};
```

## Performance Best Practices

### 1. Lazy Loading

```typescript
// Lazy load heavy components
const HeavyComponent = lazy(() => import("./HeavyComponent"))

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HeavyComponent />
    </Suspense>
  )
}
```

### 2. Efficient Storage Usage

```typescript
// Batch storage operations
const batchUpdate = async () => {
  await Promise.all([
    storage.set("key1", "value1"),
    storage.set("key2", "value2"),
    storage.set("key3", "value3"),
  ]);
};

// Use appropriate storage area
const localStorage = new Storage({ area: "local" }); // Fast, large
const syncStorage = new Storage({ area: "sync" }); // Synced, limited
const sessionStorage = new Storage({ area: "session" }); // Temporary
```

### 3. Memory Management

```typescript
// Clean up listeners
useEffect(() => {
  const listener = (message) => {
    // Handle message
  };

  chrome.runtime.onMessage.addListener(listener);

  return () => {
    chrome.runtime.onMessage.removeListener(listener);
  };
}, []);
```

## Security Best Practices

### 1. Content Security Policy

```json
{
  "manifest": {
    "content_security_policy": {
      "extension_pages": "script-src 'self'; object-src 'self'"
    }
  }
}
```

### 2. Secure Communication

```typescript
// Validate message origins
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Verify sender
  if (
    !sender.tab ||
    !sender.tab.url?.startsWith("https://trusted-domain.com")
  ) {
    return false;
  }

  // Process message
  handleMessage(message);
  return true;
});
```

### 3. Data Sanitization

```typescript
// Sanitize user input
const sanitizeInput = (input: string): string => {
  return input.replace(
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    ""
  );
};
```

## Testing Strategies

### Unit Testing

```typescript
// __tests__/storage.test.ts
import { Storage } from "@plasmohq/storage";

describe("Storage", () => {
  let storage: Storage;

  beforeEach(() => {
    storage = new Storage({ area: "local" });
  });

  test("should store and retrieve data", async () => {
    await storage.set("test", "value");
    const result = await storage.get("test");
    expect(result).toBe("value");
  });
});
```

### Integration Testing

```typescript
// Test message passing
test("should handle background messages", async () => {
  const response = await sendToBackground({
    name: "test",
    body: { data: "test" },
  });

  expect(response.success).toBe(true);
});
```

## Common Patterns

### 1. Error Handling

```typescript
const safeAsyncOperation = async () => {
  try {
    const result = await riskyOperation();
    return { success: true, data: result };
  } catch (error) {
    console.error("Operation failed:", error);
    return { success: false, error: error.message };
  }
};
```

### 2. State Synchronization

```typescript
// Sync state across extension contexts
const useSyncedState = (key: string, defaultValue: any) => {
  const [state, setState] = useStorage(key, defaultValue);

  useEffect(() => {
    const handleStorageChange = (changes) => {
      if (changes[key]) {
        setState(changes[key].newValue);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);
    return () => chrome.storage.onChanged.removeListener(handleStorageChange);
  }, [key]);

  return [state, setState];
};
```

### 3. Conditional Rendering

```typescript
// Render based on environment
const ConditionalComponent = () => {
  if (process.env.NODE_ENV === "development") {
    return <DebugPanel />
  }

  return <ProductionComponent />
}
```

## Troubleshooting Common Issues

### 1. Content Script Not Loading

- Check manifest permissions and matches
- Verify file paths in content_scripts
- Ensure proper export statements

### 2. Storage Not Syncing

- Use correct storage area (local vs sync)
- Check storage permissions in manifest
- Verify storage key consistency

### 3. Message Passing Failures

- Validate sender context
- Check message handler registration
- Ensure proper async/await usage

### 4. UI Not Rendering

- Verify CSS injection in CSUI
- Check Shadow DOM styling
- Ensure proper React component structure

## Migration Guidelines

### From Manifest V2 to V3

1. Replace background scripts with service workers
2. Update content security policy
3. Use chrome.action instead of chrome.browserAction
4. Replace chrome.tabs.executeScript with chrome.scripting

### From Other Frameworks

1. Convert popup.html to popup.tsx
2. Migrate content scripts to Plasmo structure
3. Update storage API usage
4. Implement Plasmo messaging patterns
