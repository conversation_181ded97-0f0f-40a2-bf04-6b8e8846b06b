# Talknician Chrome Extension - Current Architecture Analysis

## Project Overview
The Talknician Chrome Extension is a Plasmo-based browser extension designed for creating and sharing knowledge guides through screen captures, recordings, and interactive tutorials.

## Current Project Structure

### Core Directories
```
src/
├── background/           # Background service worker and API integrations
├── components/          # Reusable UI components
├── contents/           # Content scripts for page injection
├── hooks/              # React hooks for state management
├── services/           # API service layer
├── storage/            # Storage management utilities
├── toolbar/            # Toolbar-related components and models
├── types/              # TypeScript type definitions
└── tabs/               # Extension pages (popup, options, etc.)
```

### Key Components to Preserve

#### 1. Recording System (KEEP)
- **Location**: `src/components/recording/`, `src/hooks/useRecordingState.ts`
- **Files**:
  - `RecordingControls.tsx` - Main recording UI controls
  - `RecordingTimer.tsx` - Timer display component
  - `RecordingIcons.tsx` - Icon components for recording UI
  - `useRecordingState.ts` - Recording state management hook
- **Functionality**: Screen recording, pause/resume, timer management
- **Dependencies**: `@plasmohq/storage` for state persistence

#### 2. Toolbar System (KEEP)
- **Location**: `src/toolbar/`, `src/components/Toolbar.tsx`
- **Files**:
  - `model.js` - Toolbar configuration and tool definitions
  - `toolbar-icon.js` - Icon definitions
  - `Toolbar.tsx` - Main toolbar component
- **Functionality**: Drawing tools, capture tools, UI controls
- **Dependencies**: React, styled-components (needs Tailwind migration)

#### 3. Content Scripts (KEEP & REFACTOR)
- **Location**: `src/contents/`
- **Files**:
  - `content.tsx` - Main content script
  - `recording-content.tsx` - Recording overlay UI
- **Functionality**: Page injection, screenshot capture, recording overlay
- **Dependencies**: React, ReactDOM, Chrome APIs

#### 4. Storage Management (KEEP)
- **Location**: `src/storage/`, `src/hooks/useRecordingState.ts`
- **Files**:
  - `recording-storage.ts` - Recording state storage
- **Functionality**: Cross-tab state synchronization, persistent storage
- **Dependencies**: `@plasmohq/storage`

### Components to Remove/Replace

#### 1. Backend API Integration (REMOVE)
- **Location**: `src/background/api.ts`, `src/services/api-service.ts`
- **Files to Remove**:
  - `src/background/api.ts` - All API functions
  - `src/services/api-service.ts` - Video upload workflow
- **Functionality**: Authentication, video upload, knowledge capture APIs
- **Replacement**: Mock services with same interfaces

#### 2. Authentication System (REMOVE)
- **Location**: Various files with auth-related code
- **Code to Remove**:
  - Token management in background scripts
  - Auth message handlers
  - User profile fetching
- **Replacement**: Mock authentication state

#### 3. External Dependencies (REVIEW)
- **styled-components**: Replace with Tailwind CSS
- **uuid**: Keep for generating IDs
- **@types/uuid**: Keep

## Current Dependencies Analysis

### Keep (Core Functionality)
```json
{
  "@plasmohq/storage": "^1.15.0",    // Essential for extension storage
  "plasmo": "0.89.4",                // Framework
  "react": "18.2.0",                 // UI framework
  "react-dom": "18.2.0",             // DOM rendering
  "uuid": "^11.0.5",                 // ID generation
  "@types/uuid": "^10.0.0"           // TypeScript types
}
```

### Remove/Replace
```json
{
  "styled-components": "^6.1.13"     // Replace with Tailwind CSS
}
```

### Development Dependencies (Keep All)
All devDependencies are necessary for development workflow.

## API Integration Points (To Remove)

### 1. Background API Functions
- `uploadImage()` - Image upload to backend
- `saveKnowledgeCaptureGuide()` - Guide saving
- `uploadVideo()` - Video upload
- `fetchCurrentUserProfile()` - User profile
- `startNewChat()` - AI chat integration

### 2. Message Handlers
- `saveKnowledgeCapture` - Knowledge capture saving
- `REQUEST_AUTH_TOKEN` - Authentication token requests
- `VIDEO_PROCESSING_STARTED` - Video processing notifications

### 3. Authentication Flow
- Token storage and retrieval
- User session management
- Cross-tab authentication sync

## Chrome Extension Permissions

### Current Permissions (Keep Most)
```json
{
  "activeTab": true,        // Screenshot capture
  "scripting": true,        // Content script injection
  "storage": true,          // Local storage
  "tabs": true,             // Tab management
  "contextMenus": true,     // Right-click menus
  "sidePanel": true,        // Side panel UI
  "desktopCapture": true    // Screen recording
}
```

### Host Permissions
```json
{
  "host_permissions": ["<all_urls>"]  // Keep for content script injection
}
```

## State Management Architecture

### Current State Flow
1. **Recording State**: Managed via `@plasmohq/storage` with cross-tab sync
2. **UI State**: Local React state in components
3. **Authentication State**: Background script storage (to be removed)
4. **Capture State**: Temporary state during capture operations

### Recommended State Architecture
1. **Recording State**: Keep current implementation
2. **UI State**: Keep local React state
3. **Mock Data State**: New mock services for development
4. **Settings State**: Local storage for user preferences

## Key Findings

### Strengths
1. Well-structured Plasmo framework usage
2. Proper separation of concerns
3. Good use of React hooks for state management
4. Effective cross-tab communication for recording state

### Areas for Improvement
1. Mixed styling approaches (styled-components + some Tailwind)
2. Tight coupling with backend APIs
3. Complex authentication flow that can be simplified
4. Some components could be split into smaller modules

### Refactoring Priorities
1. **High Priority**: Remove API dependencies, implement mocks
2. **Medium Priority**: Migrate to consistent Tailwind CSS usage
3. **Low Priority**: Component splitting and optimization

## Next Steps
1. Create mock services to replace API calls
2. Remove authentication-related code
3. Standardize on Tailwind CSS for all styling
4. Update package.json dependencies
5. Create comprehensive testing strategy
