# Talknician Extension - API Specifications

## Overview

This document defines the API specifications for the Talknician Chrome Extension backend integration. These specifications serve as the contract between the frontend extension and backend services.

## Base Configuration

### API Base URL

- **Development**: `https://api-dev.talknician.com`
- **Staging**: `https://api-staging.talknician.com`
- **Production**: `https://api.talknician.com`

### Authentication

All API requests (except authentication endpoints) require a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

### Response Format

All API responses follow a consistent format:

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
    requestId: string;
  };
}

interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}
```

## 1. Authentication API

### POST /api/auth/login

Authenticate user with email and password.

**Request Body:**

```typescript
interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}
```

**Response:**

```typescript
interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  organization: Organization;
  role: UserRole;
  preferences: UserPreferences;
  createdAt: string;
}

interface Organization {
  id: string;
  name: string;
  domain: string;
  logo?: string;
  settings: OrganizationSettings;
}

type UserRole = "admin" | "member" | "viewer";
```

### POST /api/auth/refresh

Refresh authentication token.

**Request Body:**

```typescript
interface RefreshRequest {
  refreshToken: string;
}
```

**Response:**

```typescript
interface RefreshResponse {
  token: string;
  expiresAt: string;
}
```

### POST /api/auth/logout

Logout user and invalidate tokens.

**Request Body:**

```typescript
interface LogoutRequest {
  refreshToken: string;
}
```

**Response:**

```typescript
interface LogoutResponse {
  success: boolean;
}
```

### GET /api/auth/profile

Get current user profile.

**Response:**

```typescript
interface ProfileResponse {
  user: User;
}
```

## 2. Video Recording API

### POST /api/videos/upload

Upload recorded video file.

**Request:**

- Content-Type: `multipart/form-data`
- Body: FormData with video file

**Form Fields:**

```typescript
interface VideoUploadForm {
  video: File; // Video file (WebM or MP4)
  title?: string;
  description?: string;
  tags?: string[]; // JSON array as string
  metadata: string; // JSON string of VideoMetadata
}

interface VideoMetadata {
  duration: number; // Duration in seconds
  resolution: {
    width: number;
    height: number;
  };
  format: "webm" | "mp4";
  size: number; // File size in bytes
  recordedAt: string; // ISO timestamp
  pageContext?: PageContext;
}

interface PageContext {
  url: string;
  title: string;
  domain: string;
}
```

**Response:**

```typescript
interface VideoUploadResponse {
  video: Video;
  processId: string; // For tracking processing status
}

interface Video {
  id: string;
  title: string;
  description?: string;
  url: string; // Processed video URL
  thumbnailUrl: string;
  duration: number;
  resolution: Resolution;
  status: VideoStatus;
  metadata: VideoMetadata;
  createdBy: User;
  createdAt: string;
  tags: string[];
}

type VideoStatus = "uploading" | "processing" | "ready" | "failed";
```

### GET /api/videos/{id}/status

Get video processing status.

**Response:**

```typescript
interface VideoStatusResponse {
  status: VideoStatus;
  progress?: number; // 0-100 for processing
  error?: string;
  estimatedCompletion?: string; // ISO timestamp
}
```

### GET /api/videos

List user's videos with filtering and pagination.

**Query Parameters:**

```typescript
interface VideoListQuery {
  page?: number; // Default: 1
  limit?: number; // Default: 20, Max: 100
  status?: VideoStatus;
  tags?: string[]; // Filter by tags
  search?: string; // Search in title/description
  sortBy?: "createdAt" | "title" | "duration";
  sortOrder?: "asc" | "desc";
  dateFrom?: string; // ISO date
  dateTo?: string; // ISO date
}
```

**Response:**

```typescript
interface VideoListResponse {
  videos: Video[];
  pagination: PaginationMeta;
}
```

### DELETE /api/videos/{id}

Delete a video.

**Response:**

```typescript
interface VideoDeleteResponse {
  success: boolean;
}
```

## 3. Video Guides API

### GET /api/guides

Get video guides filtered by context.

**Query Parameters:**

```typescript
interface GuideListQuery {
  url?: string; // Current page URL for filtering
  domain?: string; // Domain filtering
  tags?: string[]; // Tag filtering
  search?: string; // Search query
  pinned?: boolean; // Show only pinned guides
  page?: number;
  limit?: number;
}
```

**Response:**

```typescript
interface GuideListResponse {
  guides: VideoGuide[];
  pagination: PaginationMeta;
}

interface VideoGuide {
  id: string;
  title: string;
  description: string;
  video: Video;
  tags: string[];
  pinnedUrls: string[];
  collections: Collection[];
  createdBy: User;
  createdAt: string;
  updatedAt: string;
  stats: GuideStats;
}

interface GuideStats {
  views: number;
  likes: number;
  shares: number;
  avgRating: number;
}

interface Collection {
  id: string;
  name: string;
  description?: string;
  color?: string;
}
```

### POST /api/guides

Create a new video guide.

**Request Body:**

```typescript
interface CreateGuideRequest {
  title: string;
  description?: string;
  videoId: string;
  tags?: string[];
  pinnedUrls?: string[];
  collectionIds?: string[];
}
```

**Response:**

```typescript
interface CreateGuideResponse {
  guide: VideoGuide;
}
```

### PUT /api/guides/{id}

Update a video guide.

**Request Body:**

```typescript
interface UpdateGuideRequest {
  title?: string;
  description?: string;
  tags?: string[];
  pinnedUrls?: string[];
  collectionIds?: string[];
}
```

**Response:**

```typescript
interface UpdateGuideResponse {
  guide: VideoGuide;
}
```

### POST /api/guides/{id}/pin

Pin guide to specific URL.

**Request Body:**

```typescript
interface PinGuideRequest {
  url: string;
}
```

**Response:**

```typescript
interface PinGuideResponse {
  success: boolean;
}
```

### DELETE /api/guides/{id}/pin

Unpin guide from URL.

**Request Body:**

```typescript
interface UnpinGuideRequest {
  url: string;
}
```

### GET /api/guides/{id}/transcript

Get video transcript for a guide.

**Response:**

```typescript
interface TranscriptResponse {
  transcript: Transcript;
}

interface Transcript {
  segments: TranscriptSegment[];
  language: string;
  confidence: number;
}

interface TranscriptSegment {
  startTime: number; // Seconds
  endTime: number; // Seconds
  text: string;
  confidence: number;
}
```

## 4. AI Chat API

### POST /api/chat/sessions

Create a new chat session.

**Request Body:**

```typescript
interface CreateChatSessionRequest {
  context?: PageContext;
  initialMessage?: string;
}
```

**Response:**

```typescript
interface CreateChatSessionResponse {
  session: ChatSession;
}

interface ChatSession {
  id: string;
  title?: string;
  context?: PageContext;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
}
```

### GET /api/chat/sessions

List user's chat sessions.

**Query Parameters:**

```typescript
interface ChatSessionListQuery {
  page?: number;
  limit?: number;
  search?: string;
}
```

**Response:**

```typescript
interface ChatSessionListResponse {
  sessions: ChatSession[];
  pagination: PaginationMeta;
}
```

### POST /api/chat/sessions/{sessionId}/messages

Send a message in a chat session.

**Request Body:**

```typescript
interface SendMessageRequest {
  content: string;
  context?: PageContext;
  attachments?: MessageAttachment[];
}

interface MessageAttachment {
  type: "screenshot" | "url" | "file";
  data: string; // Base64 for screenshots, URL for others
  metadata?: any;
}
```

**Response:**

```typescript
interface SendMessageResponse {
  message: ChatMessage;
  response: ChatMessage;
  suggestions?: string[];
  relatedGuides?: VideoGuide[];
}

interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  attachments?: MessageAttachment[];
  timestamp: string;
  context?: PageContext;
}
```

### GET /api/chat/sessions/{sessionId}/messages

Get messages from a chat session.

**Query Parameters:**

```typescript
interface ChatMessageListQuery {
  page?: number;
  limit?: number;
  before?: string; // Message ID for pagination
}
```

**Response:**

```typescript
interface ChatMessageListResponse {
  messages: ChatMessage[];
  pagination: PaginationMeta;
}
```

### DELETE /api/chat/sessions/{sessionId}

Delete a chat session.

**Response:**

```typescript
interface DeleteChatSessionResponse {
  success: boolean;
}
```

## 5. Collections API

### GET /api/collections

Get user's guide collections.

**Response:**

```typescript
interface CollectionListResponse {
  collections: Collection[];
}

interface Collection {
  id: string;
  name: string;
  description?: string;
  color?: string;
  guideCount: number;
  createdAt: string;
  updatedAt: string;
}
```

### POST /api/collections

Create a new collection.

**Request Body:**

```typescript
interface CreateCollectionRequest {
  name: string;
  description?: string;
  color?: string;
}
```

**Response:**

```typescript
interface CreateCollectionResponse {
  collection: Collection;
}
```

### PUT /api/collections/{id}

Update a collection.

**Request Body:**

```typescript
interface UpdateCollectionRequest {
  name?: string;
  description?: string;
  color?: string;
}
```

### DELETE /api/collections/{id}

Delete a collection.

**Response:**

```typescript
interface DeleteCollectionResponse {
  success: boolean;
}
```

## 6. Analytics API

### POST /api/analytics/events

Track user events for analytics.

**Request Body:**

```typescript
interface AnalyticsEvent {
  event: string;
  properties?: Record<string, any>;
  timestamp?: string;
  context?: PageContext;
}

interface TrackEventRequest {
  events: AnalyticsEvent[];
}
```

**Response:**

```typescript
interface TrackEventResponse {
  success: boolean;
  processed: number;
}
```

### GET /api/analytics/dashboard

Get user analytics dashboard data.

**Query Parameters:**

```typescript
interface AnalyticsDashboardQuery {
  period?: "7d" | "30d" | "90d" | "1y";
  timezone?: string;
}
```

**Response:**

```typescript
interface AnalyticsDashboardResponse {
  summary: AnalyticsSummary;
  charts: AnalyticsChart[];
}

interface AnalyticsSummary {
  totalRecordings: number;
  totalViews: number;
  totalGuides: number;
  avgRecordingDuration: number;
}

interface AnalyticsChart {
  type: "line" | "bar" | "pie";
  title: string;
  data: ChartDataPoint[];
}

interface ChartDataPoint {
  label: string;
  value: number;
  timestamp?: string;
}
```

## Error Codes

### Authentication Errors

- `AUTH_001`: Invalid credentials
- `AUTH_002`: Token expired
- `AUTH_003`: Token invalid
- `AUTH_004`: Account locked
- `AUTH_005`: Organization access denied

### Video Errors

- `VIDEO_001`: File too large
- `VIDEO_002`: Invalid file format
- `VIDEO_003`: Upload failed
- `VIDEO_004`: Processing failed
- `VIDEO_005`: Video not found

### Guide Errors

- `GUIDE_001`: Guide not found
- `GUIDE_002`: Access denied
- `GUIDE_003`: Invalid guide data
- `GUIDE_004`: Pin limit exceeded

### Chat Errors

- `CHAT_001`: Session not found
- `CHAT_002`: Message too long
- `CHAT_003`: Rate limit exceeded
- `CHAT_004`: AI service unavailable

### General Errors

- `GENERAL_001`: Invalid request format
- `GENERAL_002`: Missing required fields
- `GENERAL_003`: Server error
- `GENERAL_004`: Service unavailable
- `GENERAL_005`: Rate limit exceeded

## Rate Limiting

### Limits by Endpoint Category

- **Authentication**: 10 requests per minute
- **Video Upload**: 5 uploads per hour
- **Chat Messages**: 60 messages per minute
- **General API**: 1000 requests per hour

### Rate Limit Headers

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## WebSocket Events

### Video Processing Updates

```typescript
interface VideoProcessingEvent {
  type:
    | "video.processing.started"
    | "video.processing.progress"
    | "video.processing.completed"
    | "video.processing.failed";
  videoId: string;
  progress?: number;
  error?: string;
}
```

### Real-time Chat

```typescript
interface ChatEvent {
  type: "chat.message.received" | "chat.typing.start" | "chat.typing.stop";
  sessionId: string;
  message?: ChatMessage;
  userId?: string;
}
```

## Mock Implementation

For development purposes, mock implementations should:

1. **Return realistic data** that matches the interface specifications
2. **Simulate network delays** (200-500ms) for realistic testing
3. **Include error scenarios** for robust error handling testing
4. **Maintain state consistency** across related endpoints
5. **Support pagination** for list endpoints

### Example Mock Response

```typescript
// Mock video upload response
const mockVideoUploadResponse: ApiResponse<VideoUploadResponse> = {
  success: true,
  data: {
    video: {
      id: "video_123",
      title: "Screen Recording",
      url: "https://cdn.example.com/videos/video_123.mp4",
      thumbnailUrl: "https://cdn.example.com/thumbnails/video_123.jpg",
      duration: 120,
      status: "processing",
      // ... other fields
    },
    processId: "process_456",
  },
  meta: {
    timestamp: new Date().toISOString(),
    requestId: "req_789",
  },
};
```

```

```
