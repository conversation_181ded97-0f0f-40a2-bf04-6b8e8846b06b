# Talknician Extension - Coding Standards

## Table of Contents

1. [TypeScript Standards](#typescript-standards)
2. [React Component Guidelines](#react-component-guidelines)
3. [File Organization](#file-organization)
4. [Naming Conventions](#naming-conventions)
5. [Tailwind CSS Guidelines](#tailwind-css-guidelines)
6. [Code Formatting](#code-formatting)
7. [Error Handling](#error-handling)
8. [Testing Standards](#testing-standards)

## TypeScript Standards

### Type Definitions

```typescript
// Use explicit types for function parameters and return values
function processRecording(
  videoBlob: Blob,
  options: RecordingOptions
): Promise<ProcessingResult> {
  // Implementation
}

// Use interfaces for object shapes
interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  recordingTime: string;
  isUIVisible: boolean;
}

// Use type unions for specific values
type RecordingStatus = "idle" | "recording" | "paused" | "processing";
type CaptureType = "screen" | "tab" | "window";
```

### Generic Types

```typescript
// Use generics for reusable components
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Generic hook example
function useAsyncState<T>(initialValue: T): [T, (value: T) => void, boolean] {
  // Implementation
}
```

### Utility Types

```typescript
// Use utility types for transformations
type PartialRecordingState = Partial<RecordingState>;
type RequiredSettings = Required<UserSettings>;
type RecordingKeys = keyof RecordingState;
```

## React Component Guidelines

### Component Structure

```typescript
// Functional component with proper typing
interface RecordingControlsProps {
  onStart: () => void
  onStop: () => void
  isRecording: boolean
  className?: string
}

const RecordingControls: React.FC<RecordingControlsProps> = ({
  onStart,
  onStop,
  isRecording,
  className = ""
}) => {
  // Hooks at the top
  const [isVisible, setIsVisible] = useState(true)
  const { recordingState } = useRecordingState()

  // Event handlers
  const handleStart = useCallback(() => {
    onStart()
  }, [onStart])

  // Effects
  useEffect(() => {
    // Effect logic
  }, [])

  // Early returns
  if (!isVisible) {
    return null
  }

  // Main render
  return (
    <div className={`recording-controls ${className}`}>
      {/* Component content */}
    </div>
  )
}

export default RecordingControls
```

### Hook Guidelines

```typescript
// Custom hooks should start with 'use'
function useRecordingTimer() {
  const [time, setTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);

  // Hook logic

  return {
    time,
    isRunning,
    start: () => setIsRunning(true),
    stop: () => setIsRunning(false),
    reset: () => setTime(0),
  };
}

// Use proper dependency arrays
useEffect(() => {
  // Effect logic
}, [dependency1, dependency2]);

// Use useCallback for event handlers passed as props
const handleClick = useCallback(
  (event: MouseEvent) => {
    // Handler logic
  },
  [dependency]
);
```

### Component Composition

```typescript
// Prefer composition over inheritance
const RecordingPanel = () => {
  return (
    <Panel>
      <PanelHeader>
        <RecordingTimer />
      </PanelHeader>
      <PanelBody>
        <RecordingControls />
      </PanelBody>
    </Panel>
  )
}

// Use render props for flexible components
interface RenderProps<T> {
  data: T
  loading: boolean
  error?: string
}

const DataProvider = <T,>({
  children
}: {
  children: (props: RenderProps<T>) => React.ReactNode
}) => {
  // Provider logic
  return children({ data, loading, error })
}
```

## File Organization

### Directory Structure

```
src/
├── components/
│   ├── common/              # Shared components
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   └── index.ts
│   │   └── Modal/
│   ├── recording/           # Feature-specific components
│   │   ├── RecordingControls/
│   │   ├── RecordingTimer/
│   │   └── RecordingOverlay/
│   └── toolbar/
├── hooks/                   # Custom React hooks
│   ├── useRecordingState.ts
│   ├── useStorage.ts
│   └── index.ts
├── services/               # Business logic
│   ├── mockApi.ts
│   ├── recordingService.ts
│   └── index.ts
├── types/                  # Type definitions
│   ├── recording.ts
│   ├── api.ts
│   └── index.ts
├── utils/                  # Utility functions
│   ├── formatTime.ts
│   ├── validation.ts
│   └── index.ts
└── constants/              # Application constants
    ├── recording.ts
    └── index.ts
```

### File Naming

- **Components**: PascalCase (`RecordingTimer.tsx`)
- **Hooks**: camelCase starting with 'use' (`useRecordingState.ts`)
- **Utilities**: camelCase (`formatTime.ts`)
- **Types**: camelCase (`recordingTypes.ts`)
- **Constants**: camelCase (`recordingConstants.ts`)
- **Test files**: Same as source with `.test.` or `.spec.` (`Button.test.tsx`)

### Index Files

```typescript
// components/recording/index.ts
export { default as RecordingControls } from "./RecordingControls";
export { default as RecordingTimer } from "./RecordingTimer";
export { default as RecordingOverlay } from "./RecordingOverlay";

// types/index.ts
export type { RecordingState, RecordingOptions } from "./recording";
export type { ApiResponse, ErrorResponse } from "./api";
```

## Naming Conventions

### Variables and Functions

```typescript
// Use camelCase for variables and functions
const recordingDuration = 0;
const isRecordingActive = false;

// Use descriptive names
const handleRecordingStart = () => {};
const calculateRecordingDuration = (startTime: number) => {};

// Use verb-noun pattern for functions
const startRecording = () => {};
const stopRecording = () => {};
const pauseRecording = () => {};
```

### Constants

```typescript
// Use SCREAMING_SNAKE_CASE for constants
const MAX_RECORDING_DURATION = 3600000; // 1 hour in ms
const DEFAULT_RECORDING_OPTIONS = {
  audio: true,
  video: true,
};

// Group related constants
const RECORDING_STATES = {
  IDLE: "idle",
  RECORDING: "recording",
  PAUSED: "paused",
  PROCESSING: "processing",
} as const;
```

### Component Props

```typescript
// Use descriptive prop names
interface ButtonProps {
  onClick: () => void;
  isLoading?: boolean;
  isDisabled?: boolean;
  variant?: "primary" | "secondary";
  size?: "small" | "medium" | "large";
}

// Use 'on' prefix for event handlers
interface RecordingProps {
  onStart: () => void;
  onStop: () => void;
  onPause: () => void;
  onResume: () => void;
}
```

## Tailwind CSS Guidelines

### Class Organization

```typescript
// Order classes by category: layout, spacing, typography, colors, effects
const buttonClasses = [
  // Layout
  "flex items-center justify-center",
  // Spacing
  "px-4 py-2 m-2",
  // Typography
  "text-sm font-medium",
  // Colors
  "bg-blue-600 text-white",
  // Effects
  "rounded-lg shadow-md hover:bg-blue-700 transition-colors",
].join(" ");

// Use template literals for conditional classes
const getButtonClasses = (variant: string, isDisabled: boolean) => `
  flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors
  ${variant === "primary" ? "bg-blue-600 text-white hover:bg-blue-700" : "bg-gray-200 text-gray-800 hover:bg-gray-300"}
  ${isDisabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
`;
```

### Responsive Design

```typescript
// Use mobile-first approach
const responsiveClasses = `
  w-full
  sm:w-auto
  md:w-1/2
  lg:w-1/3
  xl:w-1/4
`;

// Group responsive variants
const gridClasses = `
  grid grid-cols-1
  sm:grid-cols-2
  md:grid-cols-3
  lg:grid-cols-4
`;
```

### Custom Utilities

```typescript
// Create reusable class combinations
const BUTTON_VARIANTS = {
  primary: "bg-blue-600 text-white hover:bg-blue-700",
  secondary: "bg-gray-200 text-gray-800 hover:bg-gray-300",
  danger: "bg-red-600 text-white hover:bg-red-700",
};

const SPACING = {
  xs: "p-1",
  sm: "p-2",
  md: "p-4",
  lg: "p-6",
  xl: "p-8",
};
```

## Code Formatting

### Prettier Configuration

```json
{
  "semi": false,
  "singleQuote": false,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "always"
}
```

### ESLint Rules

```json
{
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

## Error Handling

### Error Types

```typescript
// Define specific error types
class RecordingError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'RecordingError'
  }
}

// Use error boundaries for React components
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />
    }
    return this.props.children
  }
}
```

### Async Error Handling

```typescript
// Use Result pattern for async operations
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

const safeAsyncOperation = async (): Promise<Result<string>> => {
  try {
    const result = await riskyOperation();
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
    };
  }
};

// Use in components
const MyComponent = () => {
  const [result, setResult] = useState<Result<string> | null>(null);

  const handleOperation = async () => {
    const result = await safeAsyncOperation();
    setResult(result);

    if (!result.success) {
      console.error("Operation failed:", result.error);
    }
  };
};
```

## Testing Standards

### Unit Test Structure

```typescript
// RecordingTimer.test.tsx
import { render, screen } from '@testing-library/react'
import { RecordingTimer } from './RecordingTimer'

describe('RecordingTimer', () => {
  beforeEach(() => {
    // Setup before each test
  })

  afterEach(() => {
    // Cleanup after each test
  })

  it('should display initial time as 00:00', () => {
    render(<RecordingTimer initialTime={0} />)
    expect(screen.getByText('00:00')).toBeInTheDocument()
  })

  it('should format time correctly', () => {
    render(<RecordingTimer initialTime={65000} />)
    expect(screen.getByText('01:05')).toBeInTheDocument()
  })

  it('should handle timer updates', async () => {
    const onTimeUpdate = jest.fn()
    render(<RecordingTimer onTimeUpdate={onTimeUpdate} />)

    // Test timer functionality
  })
})
```

### Integration Tests

```typescript
// recordingFlow.test.tsx
import { renderHook, act } from "@testing-library/react";
import { useRecordingState } from "../hooks/useRecordingState";

describe("Recording Flow Integration", () => {
  it("should handle complete recording flow", async () => {
    const { result } = renderHook(() => useRecordingState());

    // Start recording
    act(() => {
      result.current.startRecording();
    });
    expect(result.current.recordingState.isRecording).toBe(true);

    // Pause recording
    act(() => {
      result.current.pauseRecording();
    });
    expect(result.current.recordingState.isPaused).toBe(true);

    // Stop recording
    act(() => {
      result.current.stopRecording();
    });
    expect(result.current.recordingState.isRecording).toBe(false);
  });
});
```

### Mock Patterns

```typescript
// Mock Chrome APIs
const mockChrome = {
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
    },
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
    },
  },
};

// Mock custom hooks
jest.mock("../hooks/useRecordingState", () => ({
  useRecordingState: () => ({
    recordingState: {
      isRecording: false,
      isPaused: false,
      recordingTime: "00:00",
    },
    startRecording: jest.fn(),
    stopRecording: jest.fn(),
  }),
}));
```
