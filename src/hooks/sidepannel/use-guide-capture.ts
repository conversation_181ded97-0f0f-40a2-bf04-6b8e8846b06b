import { useEffect, useState } from "react";
import { useStorage } from "@plasmohq/storage/hook";
import { guideManager, type Guide, type Step } from "~background/guide-manager";
import { getWebsiteUrl } from "~utils/domain-config";

const guideStorage = guideManager.getStorage();
export function useGuideCapture() {
  const [currentGuide, setCurrentGuide] = useStorage<Guide>(
    {
      key: "currentGuide",
      instance: guideStorage,
    },
    {
      id: guideManager.generateGuideId(),
      title: "Untitled Guide",
      startTime: new Date().toISOString(),
      steps: [],
      endTime: null,
    }
  );

  const [isRecording] = useStorage<boolean>({
    key: "is-recording",
    instance: guideStorage,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [title, setTitle] = useState("Untitled Guide");

  // Listen for step additions and capture screenshots
  useEffect(() => {
    const handleMessage = async (message: {
      action: string;
      step: Step;
      element: string;
    }) => {
      if (message.action === "addStep" && currentGuide) {
        // Convert base64 to blob and create URL
        const response = await fetch(message.step.screenshot);
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        // Add step with screenshot to guide
        const updatedGuide = {
          id: currentGuide.id,
          title: currentGuide.title,
          startTime: currentGuide.startTime,
          steps: [
            ...currentGuide.steps,
            { ...message.step, screenshot: blobUrl },
          ],
          endTime: currentGuide.endTime,
          uuid: currentGuide.uuid,
        };
        guideManager.getStorage().set("currentGuide", updatedGuide);
      } else if (message.action === "updateInputStep" && currentGuide) {
        // Update the last step if it matches the element
        const lastStepIndex = currentGuide.steps.length - 1;
        if (
          lastStepIndex >= 0 &&
          currentGuide.steps[lastStepIndex].element === message.element
        ) {
          // Convert base64 to blob and create URL
          const response = await fetch(message.step.screenshot);
          const blob = await response.blob();
          const blobUrl = URL.createObjectURL(blob);

          // Clean up old blob URL
          if (currentGuide.steps[lastStepIndex].screenshot) {
            URL.revokeObjectURL(currentGuide.steps[lastStepIndex].screenshot);
          }

          // Update the step
          const updatedSteps = [...currentGuide.steps];
          updatedSteps[lastStepIndex] = {
            ...message.step,
            screenshot: blobUrl,
          };

          const updatedGuide = {
            ...currentGuide,
            steps: updatedSteps,
          };
          guideManager.getStorage().set("currentGuide", updatedGuide);
        }
      }
    };

    // Add message listener
    chrome.runtime.onMessage.addListener(handleMessage);

    // Cleanup
    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, [currentGuide]);

  const handleExport = async () => {
    if (!currentGuide) return;

    setIsExporting(true);
    try {
      const response = await new Promise<{ guideJSON: Guide; error?: string }>(
        (resolve) => {
          chrome.runtime.sendMessage(
            {
              action: "exportGuideAsJSON",
              title: title,
            },
            resolve
          );
        }
      );
      if (response.error) {
        setError(response.error);
        return;
      }
      // Create and download JSON file
      chrome.runtime.sendMessage(
        {
          action: "openTalknician",
          url: await getGuideUrl(response.guideJSON.uuid),
        },
        (response) => {
          deleteCurrentGuide();
          chrome.sidePanel.setOptions({
            enabled: false,
            path: "tabs/sidepanel.html",
          });
          console.log(response);
        }
      );
    } catch (error) {
      console.error("Failed to export guide:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleStopRecording = () => {
    guideManager.stopRecording();
  };

  const handleStartRecording = () => {
    // Clear any existing guide before starting a new one
    setCurrentGuide({
      id: guideManager.generateGuideId(),
      title: "Untitled Guide",
      startTime: new Date().toISOString(),
      steps: [],
      endTime: null,
    });
    guideManager.startRecording();
    guideManager.getStorage().set("is-recording", true);
    return true;
  };

  const deleteStep = async (stepIndex: number) => {
    if (!currentGuide) return;

    // Get the step being deleted to clean up its blob URL
    const stepToDelete = currentGuide.steps[stepIndex];
    if (stepToDelete.screenshot) {
      // Clean up the blob URL
      URL.revokeObjectURL(stepToDelete.screenshot);
    }

    const updatedSteps = currentGuide.steps.filter(
      (_, index) => index !== stepIndex
    );
    const updatedGuide = {
      ...currentGuide,
      steps: updatedSteps,
    };

    // Update both the local state and storage
    setCurrentGuide(updatedGuide);
  };

  const deleteCurrentGuide = async () => {
    if (!currentGuide) return;

    // Clean up all blob URLs
    currentGuide.steps.forEach((step) => {
      if (step.screenshot) {
        URL.revokeObjectURL(step.screenshot);
      }
    });

    // Clear the current guide
    setCurrentGuide(null);
  };

  const getGuideUrl = async (guideUuid: string) => {
    const apiUrl = await getWebsiteUrl();
    return `${apiUrl}/guide/${guideUuid}`;
  };

  const updateStepDescription = async (
    stepIndex: number,
    description: string
  ) => {
    if (!currentGuide) return;

    const updatedSteps = [...currentGuide.steps];
    updatedSteps[stepIndex] = {
      ...updatedSteps[stepIndex],
      description,
    };

    const updatedGuide = {
      ...currentGuide,
      steps: updatedSteps,
    };

    // Update both local state and storage
    await guideStorage.set("currentGuide", updatedGuide);
  };

  return {
    currentGuide,
    isRecording,
    isExporting,
    handleExport,
    handleStopRecording,
    handleStartRecording,
    deleteStep,
    deleteCurrentGuide,
    error,
    setError,
    title,
    setTitle,
    updateStepDescription,
  };
}
