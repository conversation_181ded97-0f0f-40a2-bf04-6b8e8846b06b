import { useStorage } from "@plasmohq/storage/hook";
import { useEffect, useState, useCallback, useRef } from "react";
import {
  defaultRecordingState,
  recordingStorage as storage,
  getRecordingState,
  recordingStorage,
} from "~storage/recording-storage";
import type { RecordingState } from "~storage/recording-storage";

// Create a BroadcastChannel for cross-tab communication
let broadcastChannel: BroadcastChannel | null = null;
try {
  broadcastChannel = new BroadcastChannel("recording_state_sync");
} catch (error) {
  console.error("BroadcastChannel not supported:", error);
}

// Hook to access the recording state
export function useRecordingState() {
  const [recordingState, setRecordingState] = useStorage<RecordingState>(
    {
      key: "recording-state",
      instance: recordingStorage,
    },
    defaultRecordingState
  );

  // Use a ref to track the last update time to prevent update loops
  const lastUpdateTimeRef = useRef<number>(0);
  const updateThrottleMs = 50; // Minimum time between updates

  // Function to fetch the latest state
  const refreshState = useCallback(async () => {
    try {
      // Throttle updates to prevent excessive refreshes
      const now = Date.now();
      if (now - lastUpdateTimeRef.current < updateThrottleMs) {
        return;
      }
      lastUpdateTimeRef.current = now;

      const state = await getRecordingState();
      setRecordingState(state);
    } catch (error) {
      console.error("Error refreshing recording state:", error);
    }
  }, []);

  useEffect(() => {
    // Get initial state
    refreshState();

    // Set up listener for state changes from storage
    storage.watch({
      "recording-state": (change) => {
        if (change.newValue) {
          const now = Date.now();
          if (now - lastUpdateTimeRef.current < updateThrottleMs) {
            return;
          }
          lastUpdateTimeRef.current = now;

          setRecordingState(change.newValue as RecordingState);

          // Only log occasionally to reduce console spam
          if (now % 1000 < 100) {
            // Log roughly every 2 seconds
            console.log(
              "Recording state updated from storage:",
              change.newValue
            );
          }

          // Broadcast the update to other tabs
          if (broadcastChannel) {
            broadcastChannel.postMessage({
              type: "state_update",
              state: change.newValue,
              timestamp: now,
            });
          }
        }
      },
    });

    // Set up listener for BroadcastChannel messages from other tabs
    const handleBroadcastMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === "state_update") {
        const { state, timestamp } = event.data;

        // Only update if the message is newer than our last update
        if (timestamp > lastUpdateTimeRef.current) {
          lastUpdateTimeRef.current = Date.now();
          setRecordingState(state as RecordingState);

          // Only log occasionally to reduce console spam
          if (Date.now() % 1000 < 100) {
            // Log roughly every 2 seconds
            console.log("Recording state updated from broadcast:", state);
          }
        }
      }
    };

    if (broadcastChannel) {
      broadcastChannel.addEventListener("message", handleBroadcastMessage);
    }

    // Set up periodic refresh to ensure UI stays in sync
    const refreshInterval = setInterval(() => {
      refreshState();
    }, 500); // Refresh every 500ms to keep in sync

    return () => {
      storage.unwatchAll();
      clearInterval(refreshInterval);

      if (broadcastChannel) {
        broadcastChannel.removeEventListener("message", handleBroadcastMessage);
      }
    };
  }, [refreshState]);

  // Functions to update the recording state
  const startRecording = () => {
    chrome.runtime.sendMessage({ action: "startRecording" });
  };

  const pauseRecording = () => {
    chrome.runtime.sendMessage({ action: "pauseRecording" });
  };

  const resumeRecording = () => {
    chrome.runtime.sendMessage({ action: "resumeRecording" });
  };

  const stopRecording = () => {
    chrome.runtime.sendMessage({ action: "stopRecording" });
  };

  const resetRecording = () => {
    chrome.runtime.sendMessage({ action: "resetRecording" });
  };

  const toggleMicAudio = () => {
    chrome.runtime.sendMessage({ action: "toggleMicAudio" });
  };

  const toggleSystemAudio = () => {
    chrome.runtime.sendMessage({ action: "toggleSystemAudio" });
  };

  const setCaptureType = (type: "tab" | "screen") => {
    chrome.runtime.sendMessage({ action: "setCaptureType", captureType: type });
  };

  const toggleUIVisibility = () => {
    chrome.runtime.sendMessage({ action: "toggleUIVisibility" });
  };

  const openRecordingPage = () => {
    chrome.runtime.sendMessage({ action: "openRecordingPage" });
  };

  const showUI = () => {
    chrome.runtime.sendMessage({ action: "showUI" });
  };

  const hideUI = () => {
    chrome.runtime.sendMessage({ action: "hideUI" });
  };

  return {
    recordingState,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    resetRecording,
    toggleMicAudio,
    toggleSystemAudio,
    setCaptureType,
    toggleUIVisibility,
    showUI,
    hideUI,
    openRecordingPage,
    refreshState, // Expose the refresh function
  };
}
