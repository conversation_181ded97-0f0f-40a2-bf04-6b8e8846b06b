import { useEffect, useRef, useState } from "react";

export const useDraggable = () => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [offset, setOffset] = useState({ x: 0, y: 0 });

  // Handle mouse down event
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();

    // Calculate the offset
    const offsetX = e.clientX - rect.left;
    const offsetY = e.clientY - rect.top;

    setOffset({ x: offsetX, y: offsetY });
    setIsDragging(true);
  };

  // Handle mouse move event
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      // Calculate new position
      const x = e.clientX - offset.x;
      const y = e.clientY - offset.y;

      setPosition({ x, y });
    };

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
      }
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, offset]);

  // Set initial position and apply position during drag
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Set initial position at bottom left
    if (!isDragging && position.x === 0 && position.y === 0) {
      element.style.position = "fixed";
      element.style.bottom = "0";
      element.style.left = "0";
      element.style.top = "auto";
      element.style.right = "auto";
      element.style.zIndex = "9999";
    }
    // Apply position during drag
    else if (isDragging) {
      if (position.x <= 0 && position.y <= 0) {
        return;
      }

      element.style.position = "fixed";
      element.style.left = `${position.x}px`;
      element.style.top = `${position.y}px`;
      element.style.right = "auto";
      element.style.bottom = "auto";
    }
  }, [position, isDragging]);

  const resetPostition = () => {
    setPosition({ x: 0, y: 0 });
  };

  return {
    ref: elementRef,
    onMouseDown: handleMouseDown,
    resetPostition,
    style: {
      cursor: isDragging ? "grabbing" : "grab",
      userSelect: "none" as const,
    },
  };
};

export default useDraggable;
