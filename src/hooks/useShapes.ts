import { useState, useCallback } from "react";
import type { Tool } from "../types";

export interface Shape {
  id: number;
  type: Tool;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  text?: string;
  startX?: number;
  startY?: number;
  endX?: number;
  endY?: number;
  blurAmount?: number;
}

export const useShapes = () => {
  const [shapes, setShapes] = useState<Shape[]>([]);
  const [selectedTool, setSelectedTool] = useState<Tool>("cursor");
  const [currentColor, setCurrentColor] = useState("#FF0000");
  const [isDrawing, setIsDrawing] = useState(false);
  const [history, setHistory] = useState<Shape[][]>([[]]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [selectedShapeIndex, setSelectedShapeIndex] = useState<number | null>(
    null
  );
  const [isTyping, setIsTyping] = useState(false);
  const [textPosition, setTextPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [currentText, setCurrentText] = useState("");
  const [selectedTextId, setSelectedTextId] = useState<number | null>(null);

  const addToHistory = useCallback(
    (newShapes: Shape[]) => {
      setHistory((prev) => [...prev.slice(0, historyIndex + 1), newShapes]);
      setHistoryIndex((prev) => prev + 1);
    },
    [historyIndex]
  );

  const handleMouseDown = useCallback(
    ({ x, y }: { x: number; y: number }) => {
      if (selectedTool === "text") {
        const newShape: Shape = {
          id: Date.now(),
          type: "text",
          color: currentColor,
          x,
          y,
          width: 0,
          height: 0,
          text: "",
        };

        const newShapes = [...shapes, newShape];
        setShapes(newShapes);

        // Immediately open text input
        setIsTyping(true);
        setTextPosition({ x, y });
        setCurrentText("");
        return;
      }

      if (selectedTool === "cursor") {
        const clickedText = shapes.findIndex(
          (shape) =>
            shape.type === "text" &&
            x >= shape.x &&
            x <= shape.x + 200 &&
            y >= shape.y &&
            y <= shape.y + 24
        );

        if (clickedText !== -1) {
          setSelectedTextId(shapes[clickedText].id);
          setIsTyping(true);
          setTextPosition({
            x: shapes[clickedText].x,
            y: shapes[clickedText].y,
          });
          setCurrentText(shapes[clickedText].text || "");
          return;
        }
      }

      setIsDrawing(true);

      const newShape: Shape = {
        id: Date.now(),
        type: selectedTool,
        color: currentColor,
        x,
        y,
        width: 0,
        height: 0,
        startX: x,
        startY: y,
        endX: x,
        endY: y,
      };

      if (selectedTool === "cursor") {
        // Do nothing for cursor tool
      } else if (selectedTool === "blur") {
        newShape.blurAmount = 10;
      }

      const newShapes = [...shapes, newShape];
      setShapes(newShapes);
      addToHistory(newShapes);
    },
    [selectedTool, currentColor, shapes, addToHistory]
  );

  const handleMouseMove = useCallback(
    ({ x, y }: { x: number; y: number }) => {
      if (!isDrawing) return;

      const newShapes = [...shapes];
      const lastShape = newShapes[newShapes.length - 1];

      if (lastShape) {
        switch (lastShape.type) {
          case "line":
          case "arrow":
            lastShape.endX = x;
            lastShape.endY = y;
            break;
          case "rectangle":
          case "circle":
          case "blur":
            lastShape.width = x - lastShape.x;
            lastShape.height = y - lastShape.y;
            break;
        }
        setShapes(newShapes);
      }
    },
    [isDrawing, shapes]
  );

  const handleMouseUp = useCallback(() => {
    if (isDrawing) {
      addToHistory([...shapes]);
    }
    setIsDrawing(false);
  }, [isDrawing, shapes, addToHistory]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex((prev) => prev - 1);
      setShapes(history[historyIndex - 1]);
    }
  }, [history, historyIndex]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex((prev) => prev + 1);
      setShapes(history[historyIndex + 1]);
    }
  }, [history, historyIndex]);

  const deleteSelectedShape = useCallback(() => {
    if (selectedShapeIndex !== null) {
      const newShapes = shapes.filter((_, i) => i !== selectedShapeIndex);
      setShapes(newShapes);
      addToHistory(newShapes);
      setSelectedShapeIndex(null);
    }
  }, [selectedShapeIndex, shapes, addToHistory]);

  const handleTextSubmit = useCallback(
    (text: string) => {
      if (text.trim()) {
        const newShapes = [...shapes];
        const lastShape = newShapes[newShapes.length - 1];

        if (lastShape && lastShape.type === "text") {
          lastShape.text = text;
          setShapes(newShapes);
          addToHistory(newShapes);
        }
      } else {
        // Remove the shape if no text was entered
        setShapes(shapes.slice(0, -1));
      }

      setIsTyping(false);
      setTextPosition(null);
      setCurrentText("");
    },
    [shapes, addToHistory]
  );

  return {
    shapes,
    selectedTool,
    currentColor,
    isDrawing,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    setSelectedTool,
    setCurrentColor,
    undo,
    redo,
    deleteSelectedShape,
    history,
    historyIndex,
    isTyping,
    textPosition,
    currentText,
    setCurrentText,
    handleTextSubmit,
    selectedTextId,
  };
};
