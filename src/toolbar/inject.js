import { toolbar_model } from "./model";
import { Storage } from "@plasmohq/storage";

const storage = new Storage();

const el = document.createElement("div");
el.id = "talknician-toolbar";
const shadowRoot = el.attachShadow({ mode: "open" });

shadowRoot.innerHTML = `
  <ol>
    ${Object.entries(toolbar_model)
      .filter(([key, tool]) => !["houston", "capture"].includes(tool.tool))
      .reduce(
        (list, [key, tool]) => `
        ${list}
        <li id="${tool.tool}-button" aria-label="${tool.tool} tool" aria-description="${tool.description}" aria-hotkey="${key}" data-tool="${tool.tool}" data-tooltip="${tool.description}">
          ${tool.icon}
          <span class="tooltip">${tool.description}</span>
        </li>
      `,
        ""
      )}
  </ol>
`;

el.style.position = "fixed";
el.style.cursor = "grab";

let isDragging = false;
let dragStartX = 0;
let dragStartY = 0;
const DRAG_THRESHOLD = 5; // pixels to move before considering it a drag

el.addEventListener("mousedown", (e) => {
  el.style.cursor = "grabbing";
  isDragging = false;
  dragStartX = e.clientX;
  dragStartY = e.clientY;

  const shiftX = e.clientX - el.getBoundingClientRect().left;
  const shiftY = e.clientY - el.getBoundingClientRect().top;

  function moveAt(pageX, pageY) {
    const toolbar = el.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let newX = pageX - shiftX;
    let newY = pageY - shiftY;

    // Check if we've moved enough to consider it a drag
    const deltaX = Math.abs(pageX - dragStartX);
    const deltaY = Math.abs(pageY - dragStartY);
    if (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD) {
      isDragging = true;
    }

    newX = Math.max(0, Math.min(newX, viewportWidth - toolbar.width));
    newY = Math.max(0, Math.min(newY, viewportHeight - toolbar.height));

    el.style.left = newX + "px";
    el.style.top = newY + "px";
  }

  function onMouseMove(e) {
    moveAt(e.pageX, e.pageY);
  }

  document.addEventListener("mousemove", onMouseMove);

  el.addEventListener(
    "mouseup",
    () => {
      document.removeEventListener("mousemove", onMouseMove);
      el.style.cursor = "grab";
    },
    { once: true }
  );
});

el.ondragstart = () => false;

// Prevent click events if we were dragging
el.addEventListener(
  "click",
  (e) => {
    if (isDragging) {
      e.stopPropagation();
      e.preventDefault();
    }
  },
  true
);

// Add click handlers after the drag prevention
shadowRoot.getElementById("recording-button").addEventListener("click", (e) => {
  if (!isDragging) toggleRecording();
});

shadowRoot
  .getElementById("recording_audio-button")
  .addEventListener("click", (e) => {
    if (!isDragging) recordingWithAudio();
  });

shadowRoot
  .getElementById("pin_marker-button")
  .addEventListener("click", (e) => {
    if (!isDragging) handleToolClick("pin_marker");
  });

shadowRoot.getElementById("hide_pins-button").addEventListener("click", (e) => {
  if (!isDragging) handleToolClick("hide_pins");
});

shadowRoot
  .getElementById("talknician-button")
  .addEventListener("click", (e) => {
    if (!isDragging) openTalknician();
  });

shadowRoot.getElementById("close-button").addEventListener("click", (e) => {
  if (!isDragging) closeToolbar();
});

const ToolbarStyles = new CSSStyleSheet();
ToolbarStyles.replaceSync(
  `
:host {
  position: fixed;
  inset: 30% 60px auto auto;
  z-index: 2147483646;
  max-width: min-content;
  background: transparent;
  border: none;
  overflow: visible;

  opacity: 0;
  animation: present-yourself .3s ease forwards;

  @media (prefers-reduced-motion: no-preference) {
    transform: translateX(-200%);
  }

  & [constructible-support="true"] aside {
    transition: opacity 0.3s ease, transform 0.2s ease;
  }
}

:host > ol {
  all: initial;
  cursor: inherit;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  margin: 1em 0 0.5em 1em;
  padding: 0;
  list-style-type: none;
  border-radius: 2em;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);

  &:first-of-type {
    box-shadow: 0 0.25em 0.5em hsla(0,0%,0%,10%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    background-color: hsl(0 0% 10% / 1);

    &:active {
      cursor: grabbing !important;
    }

    @media (prefers-color-scheme: dark) {
      box-shadow: 0 0.25em 0.5em hsla(0,0%,0%,50%);
    }
  }
}

:host li {
  height: 2.25em;
  width: 2.25em;
  margin: 0.05em 0.25em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;

  &#close-button{
    width: 2em;
    height: 2em;
    margin-left: 6.5px;
    margin-top: -3px;
    margin-bottom: 10px;
  }
  & .tooltip {
    font-family: "GeistSans", sans-serif;
    visibility: hidden;
    position: absolute;
    right: 120%;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 2147483647;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s;
  }

  &:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }

  & .tooltip::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent rgba(0, 0, 0, 0.8);
  }

  &:first-child { margin-top: 0.25em; }
  &:last-child { margin-bottom: 0.25em; }

  &[data-tool] {
    :not([data-active=true])&:hover {
      cursor: pointer;
      background-color: hsl(0 0% 15%);
    }
    &:active {
      background-color: hsl(0 0% 20%);
    }
  }

  &[data-active=true] {
    background-color: hsl(0 0% 20%);
    box-shadow: 0 3px 5px -2px hsl(220 40% 2% /calc(25% + 3%)), 0 7px 14px -5px hsl(220 40% 2% /calc(25% + 5%));

    & > .icon-cursor {
      stroke: #10EBF2;
    }

    & > svg:not(.icon-cursor) {
      fill: #10EBF2;
    }
  }

  @media (max-height: 768px) {
    &:is(:nth-of-type(7),:nth-of-type(8), :nth-of-type(9), :nth-of-type(10), :nth-of-type(11), :nth-of-type(12), :nth-of-type(13)) > aside {
      top: auto;
    }
  }
}

:host [colors] {
  margin-top: .5em;
}

:host [colors] > li {
  overflow: hidden;
  border-radius: 50%;
  box-shadow: 0 0 0 2px hsl(0 0% 10%), 0 0.25em 0.5em hsla(0,0%,0%,25%);
  background: var(--contextual_color);
  margin-bottom: 0.5em;

  &:first-child {
    margin-top: 0;
  }
}

:host [colors] li:hover:after {
  top: 0;
}

:host li > svg {
  width: 50%;
}

:host li > svg.icon-cursor {
  width: 35%;
  fill: white;
  stroke: hsl(0 0% 80%);
  stroke-width: 2px;
}

:host [colors] > li > svg {
  fill: var(--icon_color);
}

:host [colors] > li > svg > rect:last-child {
  stroke: hsla(0,0%,0%,20%);
  stroke-width: 0.5px;
}

@keyframes present-yourself {
  to {
    opacity: 1;
    transform: translate3d(0,0,0);
  }
}

:host::backdrop {
  background: none !important;
}
`
);

shadowRoot.adoptedStyleSheets = [ToolbarStyles];

document.body.appendChild(el);

function toggleRecording() {
  chrome.runtime.sendMessage({ action: "toggleRecording" }, (response) => {
    if (response && response.isRecording !== undefined) {
      setIsRecording(response.isRecording);
    }
  });
}

function initiateKnowledgeCapture() {
  chrome.runtime.sendMessage({ action: "captureKnowledge" }, (response) => {
    if (response && response.success) {
      console.log("Knowledge capture initiated");
    } else {
      console.error("Failed to initiate knowledge capture");
    }
  });
}

function initiateHoustonChat() {
  chrome.runtime.sendMessage({ action: "initiateHoustonChat" }, (response) => {
    if (response && response.success) {
      console.log("Houston chat initiated");
    } else {
      console.error("Failed to initiate houston chat");
    }
  });
}

function openTalknician() {
  chrome.runtime.sendMessage(
    {
      action: "openTalknician",
      url: "https://app.talknician.com",
    },
    (response) => {
      // Optional: handle response if needed
      console.log("Talknician tab opened and activated");
    }
  );
}

function recordingWithAudio() {
  console.log("Toggling recording UI");

  // Send a message to the background script to toggle UI visibility
  chrome.runtime.sendMessage({ action: "toggleUIVisibility" }, (response) => {
    if (response && response.success) {
      // Update button state if needed
      const recordingAudioButton = shadowRoot.getElementById(
        "recording_audio-button"
      );
      if (recordingAudioButton) {
        if (response.isVisible) {
          recordingAudioButton.setAttribute("data-active", "true");
        } else {
          recordingAudioButton.removeAttribute("data-active");
        }
      } else {
        console.error("Could not find recording_audio-button element");
      }
    } else {
      console.error(
        "Failed to toggle recording UI visibility:",
        response?.error
      );
    }
  });
}

function closeToolbar() {
  chrome.runtime.sendMessage({ action: "toggleToolbar", show: false });
  // Also hide the recording controls when closing the toolbar
  chrome.runtime.sendMessage({ action: "hideUI" });
}

// Listen for messages from content script about pin creation
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "PIN_CREATED") {
    // Update toolbar UI to show pin is inactive
    const pinButton = shadowRoot.getElementById("pin_marker-button");
    if (pinButton) {
      pinButton.removeAttribute("data-active");
    }
    // Update storage state
    storage.set("talknician_pinning_active", false);
  }
});

const handleToolClick = async (tool) => {
  if (tool === "pin_marker") {
    let isPinningActive =
      (await storage.get("talknician_pinning_active")) || false;
    isPinningActive = !isPinningActive;

    chrome.runtime.sendMessage({
      action: "TOGGLE_PIN_MODE",
      active: isPinningActive,
    });

    await storage.set("talknician_pinning_active", isPinningActive);

    const pinButton = shadowRoot.getElementById("pin_marker-button");
    if (pinButton) {
      if (isPinningActive) {
        pinButton.setAttribute("data-active", "true");
      } else {
        pinButton.removeAttribute("data-active");
      }
    }

    return;
  }

  if (tool === "hide_pins") {
    let arePinsHidden = (await storage.get("talknician_pins_hidden")) || false;
    arePinsHidden = !arePinsHidden;

    chrome.runtime.sendMessage({
      action: "TOGGLE_PINS_VISIBILITY",
      hidden: arePinsHidden,
    });

    await storage.set("talknician_pins_hidden", arePinsHidden);

    const hidePinsButton = shadowRoot.getElementById("hide_pins-button");
    if (hidePinsButton) {
      if (arePinsHidden) {
        hidePinsButton.setAttribute("data-active", "true");
      } else {
        hidePinsButton.removeAttribute("data-active");
      }
    }

    return;
  }

  // ... rest of existing tool handlers ...
};
