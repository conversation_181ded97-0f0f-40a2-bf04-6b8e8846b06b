export const screen_recording = `
<svg viewBox="0 0 24 23" fill="none">
  <line
    x1="14.8517"
    y1="1.62939"
    x2="22.9998"
    y2="1.62939"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="22.3702"
    y1="1"
    x2="22.3702"
    y2="9.14815"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="1.62962"
    y1="9.14795"
    x2="1.62962"
    y2="0.999802"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="1"
    y1="1.62939"
    x2="9.14815"
    y2="1.62939"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="9.14835"
    y1="21.5559"
    x2="1.0002"
    y2="21.5559"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="1.62982"
    y1="22.1855"
    x2="1.62982"
    y2="14.0374"
    stroke="white"
    stroke-width="2"
  />
  <ellipse
    cx="18.5183"
    cy="17.7038"
    rx="1.49383"
    ry="1.49383"
    fill="#FF0909"
  />
  <circle cx="18.5185" cy="17.7036" r="3.98148" stroke="white" />
</svg>
`;

export const screen_recording_audio = `
<svg viewBox="0 0 24 23" fill="none">
  <line
    x1="14.8517"
    y1="1.62939"
    x2="22.9998"
    y2="1.62939"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="22.3702"
    y1="1"
    x2="22.3702"
    y2="9.14815"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="1.62962"
    y1="9.14795"
    x2="1.62962"
    y2="0.999802"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="1"
    y1="1.62939"
    x2="9.14815"
    y2="1.62939"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="9.14835"
    y1="21.5559"
    x2="1.0002"
    y2="21.5559"
    stroke="white"
    stroke-width="2"
  />
  <line
    x1="1.62982"
    y1="22.1855"
    x2="1.62982"
    y2="14.0374"
    stroke="white"
    stroke-width="2"
  />
  <ellipse
    cx="18.5183"
    cy="17.7038"
    rx="1.49383"
    ry="1.49383"
    fill="#FF0909"
  />
  <circle cx="18.5185" cy="17.7036" r="3.98148" stroke="white" />
  <path
    d="M4 12C4 10.3431 5.34315 9 7 9H9C10.6569 9 12 10.3431 12 12C12 13.6569 10.6569 15 9 15H7C5.34315 15 4 13.6569 4 12Z"
    stroke="white"
    stroke-width="2"
  />
  <path
    d="M12 12C12 13.6569 13.3431 15 15 15H17C18.6569 15 20 13.6569 20 12C20 10.3431 18.6569 9 17 9H15C13.3431 9 12 10.3431 12 12Z"
    stroke="white"
    stroke-width="2"
  />
</svg>
`;

export const capture = `
<svg viewBox="0 0 18 26" fill="none">
  <path
    d="M5 9C5 7.93913 5.42143 6.92172 6.17157 6.17157C6.92172 5.42143 7.93913 5 9 5M6.33333 25H11.6667M11.6667 21C11.6667 15.5333 17 14.4667 17 9C17 6.87827 16.1571 4.84344 14.6569 3.34315C13.1566 1.84286 11.1217 1 9 1C6.87827 1 4.84344 1.84286 3.34315 3.34315C1.84286 4.84344 1 6.87827 1 9C1 14.3333 6.33333 15.6667 6.33333 21H11.6667Z"
    stroke="white"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
  />
</svg>
`;

export const houston = `
<svg viewBox="0 0 24 23" fill="none">
  <path
    d="M17.5022 21.9233L17.5022 16.5053C19.2131 15.0668 20.2055 13.0174 20.2379 10.8559C20.2566 6.64216 16.7343 3.17087 12.2697 3.00328C10.052 2.94237 7.9026 3.73086 6.31142 5.18905C4.72023 6.64724 3.82285 8.65087 3.82379 10.7433V11.6033L2 15.9033L3.82379 16.7633L3.82379 20.2033C3.82379 21.1532 4.64032 21.9233 5.64757 21.9233L9.29514 21.9233C11.8133 21.9233 13.8546 19.9981 13.8546 17.6233L13.8546 13.3233"
    stroke="white"
    stroke-width="2.064"
    stroke-miterlimit="10"
    stroke-linecap="round"
  />
  <path
    d="M13.8431 13.3333C15.354 13.3333 16.5788 12.1782 16.5788 10.7533C16.5788 9.32845 15.354 8.17334 13.8431 8.17334C12.3322 8.17334 11.1074 9.32845 11.1074 10.7533C11.1074 12.1782 12.3322 13.3333 13.8431 13.3333Z"
    stroke="white"
    stroke-width="2.064"
    stroke-miterlimit="10"
    stroke-linecap="square"
  />
  <path
    d="M18.7064 7.53859L18.9913 6.62134L19.7581 6.14955L18.9131 5.81324L18.4572 4.96299L18.1749 5.87677L17.4054 6.35203L18.2531 6.68487M21.5538 6.31964L22.1177 4.48563L23.6573 3.54155L21.9619 2.87588L21.0554 1.16843L20.4915 3.00243L18.9519 3.94652L20.6473 4.61219M18.2703 3.03128L18.5555 2.11725L19.322 1.64224L18.4773 1.30915L18.021 0.455677L17.7391 1.37268L16.9693 1.84472L17.8173 2.18078"
    fill="white"
  />
</svg>
`;

export const astro = `
<svg viewBox="0 0 24 22" fill="none">
  <path
    d="M15.8156 0.927327C15.8005 0.896702 15.7773 0.870994 15.7487 0.853218C15.7202 0.835442 15.6874 0.826334 15.6543 0.826962C13.4437 0.855949 9.15704 3.9996 7.28021 7.37221C6.94448 7.97008 6.66049 8.59782 6.43146 9.24832C5.61252 9.38855 4.82941 9.66302 4.23009 10.1521C2.54137 11.5405 2.59127 13.9737 2.69758 14.9977C2.70309 15.0539 2.72115 15.1082 2.75034 15.1563C2.77953 15.2044 2.81906 15.2451 2.86581 15.2752C2.91256 15.3052 2.96526 15.3238 3.01977 15.3295C3.07428 15.3352 3.12911 15.3277 3.17994 15.3078L6.17292 14.1316C6.23939 14.3718 6.31762 14.6085 6.40733 14.8409C6.46722 15.003 6.57998 15.1392 6.72602 15.2261L8.25074 16.1407C8.39601 16.2288 8.56574 16.2625 8.73067 16.2359C8.96778 16.2011 9.20284 16.1539 9.43492 16.0946L9.94753 19.3744C9.95635 19.4301 9.97755 19.4832 10.0095 19.5294C10.0414 19.5757 10.0832 19.6139 10.1315 19.641C10.1798 19.6682 10.2334 19.6835 10.2881 19.6859C10.3428 19.6882 10.397 19.6775 10.4465 19.6546C11.3525 19.2443 13.4095 18.075 13.7198 15.8618C13.8287 15.0777 13.6698 14.2385 13.3816 13.4339C13.8109 12.9027 14.1938 12.3332 14.526 11.7319C16.4121 8.36118 16.9143 3.04295 15.8156 0.927327ZM11.0792 9.23138C10.7763 9.04928 10.531 8.77963 10.3745 8.45654C10.218 8.13344 10.1573 7.77143 10.2001 7.41628C10.2429 7.06113 10.3872 6.72881 10.6148 6.46135C10.8424 6.19388 11.1431 6.00329 11.4788 5.91368C11.8145 5.82407 12.1702 5.83947 12.5008 5.95794C12.8314 6.0764 13.1221 6.2926 13.3362 6.57919C13.5503 6.86578 13.678 7.20989 13.7034 7.56799C13.7287 7.92608 13.6505 8.28208 13.4785 8.59095C13.3646 8.7961 13.2122 8.97543 13.0299 9.11867C12.8476 9.26191 12.6391 9.36625 12.4162 9.42573C12.1934 9.48521 11.9606 9.49867 11.7312 9.46532C11.5018 9.43197 11.2802 9.35247 11.0792 9.23138Z"
    fill="white"
  />
  <path
    d="M8.06762 17.9201C7.90487 18.1587 7.6014 18.2927 7.23138 18.426C6.39968 18.7234 5.51178 18.086 5.5129 17.163C5.51373 16.813 5.60634 16.3068 5.69547 16.1764C5.71503 16.1484 5.72548 16.1146 5.72531 16.0798C5.72515 16.0451 5.71439 16.0112 5.69457 15.9829C5.67474 15.9546 5.64686 15.9334 5.61488 15.9222C5.58289 15.9111 5.54843 15.9106 5.51639 15.9208C5.04723 16.0633 4.63948 16.3642 4.35806 16.7758C3.6597 17.7985 4.13521 21.0704 4.13521 21.0704C4.13521 21.0704 7.27251 20.4502 7.97087 19.4274C8.2532 19.0159 8.39206 18.5166 8.36507 18.01C8.35182 17.8517 8.15236 17.7901 8.06762 17.9201Z"
    fill="white"
  />
  <path
    d="M18.9724 7.59187L19.243 6.66812L19.9713 6.193L19.1687 5.85431L18.7357 4.99805L18.4676 5.91829L17.7368 6.39691L18.5419 6.7321M21.6768 6.36429L22.2123 4.51731L23.6573 3.56655L22.0644 2.89618L21.2034 1.17665L20.6678 3.02362L19.2056 3.97438L20.8158 4.64476M18.5582 3.05268L18.829 2.13218L19.5571 1.65381L18.7548 1.31837L18.3215 0.458861L18.0537 1.38235L17.3226 1.85773L18.128 2.19616"
    fill="white"
  />
</svg>
`;

export const talknician = `
<svg viewBox="0 0 1948 2063" fill="none">
<g clip-path="url(#clip0_73_428)">
<mask id="mask0_73_428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="476" y="583" width="996" height="1155">
<path d="M1471.57 583.322H476.438V1737.54H1471.57V583.322Z" fill="white"/>
</mask>
<g mask="url(#mask0_73_428)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M585.676 1045.61L984.85 1020.92L1384.02 1045.61L984.85 1070.29L585.676 1045.61Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M972.17 745.75C965.967 713.16 1070.07 665.975 1204.69 640.36C1293.97 623.371 1382.89 604.437 1471.43 583.556C1459.48 608.23 1455.56 631.374 1459.67 652.99C1465.87 685.581 1361.77 732.765 1227.15 758.38C1092.54 783.995 978.372 778.341 972.17 745.75Z" fill="white"/>
<mask id="mask1_73_428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="971" y="583" width="501" height="192">
<path fill-rule="evenodd" clip-rule="evenodd" d="M972.17 745.75C965.967 713.16 1070.07 665.975 1204.69 640.36C1293.97 623.371 1382.89 604.437 1471.43 583.556C1459.48 608.23 1455.56 631.374 1459.67 652.99C1465.87 685.581 1361.77 732.765 1227.15 758.38C1092.54 783.995 978.372 778.341 972.17 745.75Z" fill="white"/>
</mask>
<g mask="url(#mask1_73_428)">
<rect width="520.791" height="126.08" transform="matrix(0.982375 -0.186922 0.18701 0.982358 959.824 680.904)" fill="url(#pattern0_73_428)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M975.711 747.274C982.076 713.842 878.104 665.975 743.485 640.36C654.198 623.371 565.291 604.386 476.764 583.405C488.616 608.637 492.431 632.34 488.209 654.514C481.845 687.946 585.817 735.813 720.437 761.428C855.056 787.043 969.347 780.706 975.711 747.274Z" fill="white"/>
<mask id="mask2_73_428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="476" y="583" width="500" height="195">
<path fill-rule="evenodd" clip-rule="evenodd" d="M975.707 747.265C982.074 713.828 878.105 665.961 743.488 640.351C654.202 623.365 565.297 604.383 476.771 583.405C488.622 608.64 492.435 632.345 488.213 654.522C481.846 687.96 585.815 735.826 720.433 761.437C855.05 787.047 969.341 780.702 975.707 747.265Z" fill="white"/>
</mask>
<g mask="url(#mask2_73_428)">
<rect width="520.78" height="129.356" transform="matrix(-0.98238 -0.186891 -0.187043 0.982352 988.375 680.734)" fill="url(#pattern1_73_428)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M885.711 748.638L975.123 1737.54L1064.53 748.638H885.711Z" fill="white"/>
</g>
<path d="M2268.71 1493.82L2249.19 1482.91L2229.68 1493.82L1358.9 1980.4L1338.42 1991.85V2015.32V2988.49V3011.96L1358.9 3023.41L2229.68 3510L2249.19 3520.9L2268.71 3510L3139.48 3023.41L3159.97 3011.96V2988.49V2015.32V1991.85L3139.48 1980.4L2268.71 1493.82Z" stroke="white" stroke-width="80"/>
</g>
<path d="M986.492 36.3448L974 29.1386L961.508 36.3448L117.999 522.93L105.491 530.146V544.586V1517.76V1532.2L117.999 1539.41L961.508 2026L974 2033.2L986.492 2026L1830 1539.41L1842.51 1532.2V1517.76V544.586V530.146L1830 522.93L986.492 36.3448Z" stroke="white" stroke-width="50"/>
<defs>
<pattern id="pattern0_73_428" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_73_428" transform="scale(0.00334448 0.0052356)"/>
</pattern>
<pattern id="pattern1_73_428" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_73_428" transform="scale(0.00334448 0.0052356)"/>
</pattern>
<clipPath id="clip0_73_428">
<rect width="995.129" height="1154.22" fill="white" transform="translate(476.438 583.322)"/>
</clipPath>
<image id="image0_73_428" width="299" height="191" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASsAAAC/CAMAAACR+gpnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAADUExURQAAAKd6PdoAAAABdFJOUwBA5thmAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAT0lEQVR4Xu3BgQAAAADDoPlTX+AIVQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABw1QDf1AAB8/VCXQAAAABJRU5ErkJggg=="/>
</defs>
</svg>
`;

export const close = `
<svg viewBox="0 0 10 9" fill="none">
  <path
    d="M8.13653 0.493652L4.95428 3.4984L1.77278 0.493652L0.71228 1.49524L3.89378 4.49999L0.71228 7.50474L1.77278 8.50632L4.95428 5.50157L8.13653 8.50632L9.19703 7.50474L6.01553 4.49999L9.19703 1.49524L8.13653 0.493652Z"
    fill="#6D8994"
  />
</svg>
`;

export const pin_marker = `
<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 4C12 2.89543 12.8954 2 14 2H18C19.1046 2 20 2.89543 20 4V8C20 9.10457 19.1046 10 18 10H17V19C17 19.5523 16.5523 20 16 20H14C13.4477 20 13 19.5523 13 19V10H12C10.8954 10 10 9.10457 10 8V4Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="16" cy="6" r="2" fill="#40F7FF"/>
</svg>
`;

export const hide_pins = `
<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 4C12 2.89543 12.8954 2 14 2H18C19.1046 2 20 2.89543 20 4V8C20 9.10457 19.1046 10 18 10H17V19C17 19.5523 16.5523 20 16 20H14C13.4477 20 13 19.5523 13 19V10H12C10.8954 10 10 9.10457 10 8V4Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="16" cy="6" r="2" fill="#40F7FF"/>
  <line x1="4" y1="20" x2="20" y2="4" stroke="white" stroke-width="2" stroke-linecap="round"/>
</svg>
`;
