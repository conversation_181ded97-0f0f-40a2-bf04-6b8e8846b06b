import {
  getRecordingState,
  setRecordingState,
  startRecording,
  pauseRecording,
  resumeRecording,
  stopRecording,
  watchRecordingState,
  toggleUIVisibility,
  showUI,
  hideUI,
} from "~storage/recording-storage";

// Timer variables
let recordingTimer: number | null = null;

class RecordingManager {
  // Store the recording tab ID
  private recordingTabId: number | null = null;

  constructor() {
    // Initialize recording state
    this.initialize();

    // Set up message listeners
  }

  async initialize() {
    // Make sure we have a default state
    await getRecordingState();

    // Watch for recording state changes
    watchRecordingState((state) => {
      // Handle state changes if needed

      // If recording was stopped, clear the timer
      if (!state.isRecording && recordingTimer !== null) {
        clearInterval(recordingTimer);
        recordingTimer = null;

        // Also close the recording tab if it exists
        this.closeRecordingTabIfExists();
      }
    });
  }

  // Helper method to close the recording tab if it exists
  private closeRecordingTabIfExists() {
    if (this.recordingTabId !== null) {
      // First check if the tab still exists
      chrome.tabs.get(this.recordingTabId, (_tab) => {
        if (chrome.runtime.lastError) {
          this.recordingTabId = null;
          return;
        }

        // If tab exists, remove it
        chrome.tabs.remove(this.recordingTabId, () => {
          // Clear the tab ID regardless of success or failure
          this.recordingTabId = null;
        });
      });
    }
  }

  // Handle uploading the recording
  async handleUploadRecording(request, sendResponse) {
    try {
      console.log("Uploading recording...", request);

      if (!request.blob) {
        console.error("No blob received in uploadRecording request");
        sendResponse({
          success: false,
          error: "No recording data received. Please try again.",
        });
        return;
      }

      // Convert the blob to a File object
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const fileExtension = request.mimeType?.includes("mp4") ? "mp4" : "webm";
      const filename = `screen-recording-${timestamp}.${fileExtension}`;

      const videoFile = new File([request.blob], filename, {
        type: request.mimeType || "video/webm",
      });

      // Import the handleVideoUploadWorkflow function
      const { handleVideoUploadWorkflow } = await import(
        "~services/api-service"
      );

      // Upload the video using the workflow
      const result = await handleVideoUploadWorkflow(videoFile);

      if (result.success && result.processId) {
        console.log(
          "Video uploaded successfully with process ID:",
          result.processId
        );

        // Close the recording tab after successful upload
        this.closeRecordingTabIfExists();

        // Trigger the openTalknician action to open the dashboard
        chrome.runtime.sendMessage({
          action: "openTalknician",
          url: `${await import("~utils/domain-config").then((m) => m.getWebsiteUrl())}/dashboard`,
        });

        sendResponse({
          success: true,
          processId: result.processId,
          message:
            "Recording uploaded successfully and processing has started.",
        });
      } else {
        console.error("Video upload failed:", result.message);
        sendResponse({
          success: false,
          error: result.message || "Upload failed for unknown reason.",
        });
      }
    } catch (error) {
      console.error("Error uploading recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // Handle canceling the recording
  async handleCancelRecording(sendResponse) {
    try {
      console.log("Canceling recording...");

      // Close the recording tab
      this.closeRecordingTabIfExists();

      console.log("Recording canceled successfully");
      sendResponse({
        success: true,
        message: "Recording canceled",
      });
    } catch (error) {
      console.error("Error canceling recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleToggleUIVisibility(sendResponse) {
    try {
      const isVisible = await toggleUIVisibility();
      sendResponse({ success: true, isVisible });
    } catch (error) {
      console.error("Error toggling UI visibility:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleShowUI(sendResponse) {
    try {
      await showUI();
      sendResponse({ success: true });
    } catch (error) {
      console.error("Error showing UI:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleHideUI(sendResponse) {
    try {
      await hideUI();
      sendResponse({ success: true });
    } catch (error) {
      console.error("Error hiding UI:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // Store the active tab ID for returning after recording starts
  private activeTabId: number | null = null;

  async handleStartRecording(
    request: any,
    sendResponse: (response: any) => void
  ) {
    try {
      console.log("Starting recording...");
      const state = await getRecordingState();

      // If already recording, don't start again
      if (state.isRecording) {
        console.log("Already recording, not starting again");
        sendResponse({ success: true, alreadyRecording: true });
        return;
      }

      // First, close any existing recording page
      await this.closeRecordingTabIfExistsAsync();

      // Start the recording state
      await startRecording();

      // Start a timer to initialize recording time
      this.startRecordingTimer();

      // Store the current active tab before opening the recording page
      this.activeTabId = null;
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length > 0) {
          this.activeTabId = tabs[0].id || null;
          console.log("Stored active tab ID:", this.activeTabId);
        }
      });

      // Open the recording page in a pinned tab
      chrome.tabs.create(
        {
          url: chrome.runtime.getURL("tabs/recording-page.html"),
          pinned: true,
        },
        (tab) => {
          if (chrome.runtime.lastError) {
            console.error(
              "Error opening recording page:",
              chrome.runtime.lastError
            );
          } else {
            // Store the tab ID for later use
            this.recordingTabId = tab.id;
            console.log("Recording tab opened with ID:", tab.id);

            // We'll return to the original tab only after the user has selected a screen
            // and the recording has actually started. This is handled in handleRecordingActuallyStarted.
          }
        }
      );

      console.log("Recording started successfully");
      sendResponse({ success: true });
    } catch (error) {
      console.error("Error starting recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // Promise-based version of closeRecordingTabIfExists
  private async closeRecordingTabIfExistsAsync(): Promise<void> {
    if (this.recordingTabId === null) {
      console.log("No recording tab to close");
      return;
    }

    try {
      // Check if the tab exists
      await new Promise<void>((resolve, reject) => {
        chrome.tabs.get(this.recordingTabId!, (_tab) => {
          if (chrome.runtime.lastError) {
            console.log(
              "Tab doesn't exist or already closed:",
              chrome.runtime.lastError.message
            );
            this.recordingTabId = null;
            resolve();
            return;
          }

          // Tab exists, remove it
          chrome.tabs.remove(this.recordingTabId!, () => {
            if (chrome.runtime.lastError) {
              console.error(
                "Error closing tab:",
                chrome.runtime.lastError.message
              );
              reject(chrome.runtime.lastError);
              return;
            }

            console.log(
              "Successfully closed recording tab:",
              this.recordingTabId
            );
            this.recordingTabId = null;
            resolve();
          });
        });
      });
    } catch (error) {
      console.error("Error in closeRecordingTabIfExistsAsync:", error);
      // Reset the tab ID even if there was an error
      this.recordingTabId = null;
    }
  }

  // Handle the notification that recording has actually started
  async handleRecordingActuallyStarted() {
    console.log("Recording actually started, returning to original tab");

    // Return to the original tab now that recording has actually started
    if (this.activeTabId) {
      chrome.tabs.update(this.activeTabId, { active: true }, () => {
        if (chrome.runtime.lastError) {
          console.error(
            "Error returning to original tab:",
            chrome.runtime.lastError
          );
        } else {
          console.log("Returned to original tab:", this.activeTabId);
        }
      });
    }
  }

  // Handle the notification that recording was stopped from the recording page
  async handleRecordingStoppedFromPage() {
    console.log(
      "Recording stopped from page, activating recording tab for preview"
    );

    // Make the recording tab active to show the preview
    if (this.recordingTabId !== null) {
      chrome.tabs.update(this.recordingTabId, { active: true }, () => {
        if (chrome.runtime.lastError) {
          console.error(
            "Error activating recording tab:",
            chrome.runtime.lastError
          );
        } else {
          console.log(
            "Activated recording tab for preview after stop from page"
          );
        }
      });
    }
  }

  async handlePauseRecording(sendResponse: (response: any) => void) {
    try {
      // Pause the recording state
      await pauseRecording();

      sendResponse({ success: true });
    } catch (error) {
      console.error("Error pausing recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleResumeRecording(sendResponse: (response: any) => void) {
    try {
      // Resume the recording state
      await resumeRecording();

      sendResponse({ success: true });
    } catch (error) {
      console.error("Error resuming recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleStopRecording(sendResponse: (response: any) => void) {
    try {
      console.log("Stopping recording...");
      const state = await getRecordingState();

      // If not recording, don't try to stop
      if (!state.isRecording) {
        console.log("Not recording, nothing to stop");
        sendResponse({ success: true, notRecording: true });
        return;
      }

      // Stop the recording state
      await stopRecording();

      // Clear the timer
      if (recordingTimer !== null) {
        clearInterval(recordingTimer);
        recordingTimer = null;
      }

      // We don't close the tab here anymore - we'll keep it open for preview
      // The tab will be closed when the user clicks "Upload" or "Cancel"

      // Make the recording tab active to show the preview
      if (this.recordingTabId !== null) {
        chrome.tabs.update(this.recordingTabId, { active: true }, () => {
          if (chrome.runtime.lastError) {
            console.error(
              "Error activating recording tab:",
              chrome.runtime.lastError
            );
          } else {
            console.log("Activated recording tab for preview");
          }
        });
      }

      console.log("Recording stopped successfully");
      sendResponse({ success: true });
    } catch (error) {
      console.error("Error stopping recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleResetRecording(sendResponse: (response: any) => void) {
    try {
      console.log("Resetting recording...");
      const state = await getRecordingState();

      // Store the current active tab before resetting
      this.activeTabId = null;
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length > 0) {
          this.activeTabId = tabs[0].id || null;
          console.log("Stored active tab ID for reset:", this.activeTabId);
        }
      });

      // First stop the current recording if it's active
      if (state.isRecording) {
        await stopRecording();

        // Clear the timer
        if (recordingTimer !== null) {
          clearInterval(recordingTimer);
          recordingTimer = null;
        }

        // Close the recording tab if it exists
        await this.closeRecordingTabIfExistsAsync();
      } else {
        // Even if not recording, make sure any existing recording tab is closed
        await this.closeRecordingTabIfExistsAsync();
      }

      // Then start a new recording
      await startRecording();

      // Start a timer to update recording time
      this.startRecordingTimer();

      // Open a new recording page in a pinned tab
      chrome.tabs.create(
        {
          url: chrome.runtime.getURL("tabs/recording-page.html"),
          pinned: true,
        },
        (tab) => {
          if (chrome.runtime.lastError) {
            console.error(
              "Error opening recording page:",
              chrome.runtime.lastError
            );
          } else {
            // Store the tab ID for later use
            this.recordingTabId = tab.id;
            console.log("Recording tab opened with ID:", tab.id);

            // We'll return to the original tab only after the user has selected a screen
            // and the recording has actually started. This is handled in handleRecordingActuallyStarted.
          }
        }
      );

      console.log("Recording reset successfully");
      sendResponse({ success: true });
    } catch (error) {
      console.error("Error resetting recording:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleToggleMicAudio(sendResponse: (response: any) => void) {
    try {
      const state = await getRecordingState();
      await setRecordingState({ micAudioEnabled: !state.micAudioEnabled });
      sendResponse({ success: true, enabled: !state.micAudioEnabled });
    } catch (error) {
      console.error("Error toggling mic audio:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleToggleSystemAudio(sendResponse: (response: any) => void) {
    try {
      const state = await getRecordingState();
      await setRecordingState({
        systemAudioEnabled: !state.systemAudioEnabled,
      });
      sendResponse({ success: true, enabled: !state.systemAudioEnabled });
    } catch (error) {
      console.error("Error toggling system audio:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleSetCaptureType(
    captureType: "tab" | "screen",
    sendResponse: (response: any) => void
  ) {
    try {
      await setRecordingState({ captureType });
      sendResponse({ success: true });
    } catch (error) {
      console.error("Error setting capture type:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // Function to start the recording timer
  async startRecordingTimer() {
    // Clear any existing timer
    if (recordingTimer !== null) {
      clearInterval(recordingTimer);
      recordingTimer = null;
    }

    console.log("Starting simplified recording timer...");

    // Set initial recording time to 00:00
    await setRecordingState({
      recordingTime: "00:00",
    });

    // We don't need to update the time in the background anymore
    // Each component will maintain its own timer based on the recording state
    console.log("Timer will be managed by individual components");
  }
}

export const recordingManager = new RecordingManager();
