import injectJS from "url:~src/toolbar/inject.js";
import ejectJS from "url:~src/toolbar/eject.js";
import { uploadImage, fetchPin, updatePin } from "./api";
import toggleToolbar from "./injected-helper";
import { guideManager } from "./guide-manager";
import { checkAuthentication } from "~utils/auth";
import { houstonManager } from "./houston-manager";
import { getWebsiteUrl } from "~utils/domain-config";
import { recordingManager } from "./recording-manager";

let FE_URL: string;
let isToolbarVisible = false;
const state = {
  loaded: {},
  injected: {},
};
var toggleIt;

const initializeFrontendUrl = async () => {
  FE_URL = await getWebsiteUrl();
};

// Initialize on module load
initializeFrontendUrl();

chrome.runtime.onUpdateAvailable.addListener(() => {
  chrome.action.setPopup({ popup: "tabs/update-notification.html" });
  chrome.action.openPopup();
  chrome.action.setPopup({ popup: "" });
});

// Handle extension installation
chrome.runtime.onInstalled.addListener(async (details) => {
  if (details.reason === "install") {
    await initializeFrontendUrl();
    // Create new tab with Talknician login page
    const loginUrl = `${FE_URL}/signin?source=extension_install`;
    const tab = await chrome.tabs.create({ url: loginUrl, active: true });

    // Focus the window containing the tab
    if (tab.windowId) {
      await chrome.windows.update(tab.windowId, { focused: true });
    }

    // Check if extension is pinned
    const action = await chrome.action.getUserSettings();
    if (!action.isOnToolbar) {
      chrome.action.setPopup({ popup: "tabs/pin-popup.html" });
      chrome.action.openPopup();
      chrome.action.setPopup({ popup: "" });
    }
  }
});

// Add tab listeners at the top level
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  const tab = await chrome.tabs.get(activeInfo.tabId);
  checkTalknicianWebsite(tab);
});
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo) => {
  if (changeInfo.status === "complete") {
    const tab = await chrome.tabs.get(tabId);
    checkTalknicianWebsite(tab);
  }
});

function checkTalknicianWebsite(tab: chrome.tabs.Tab) {
  if (tab.url?.startsWith(FE_URL)) {
    // Request auth token from the page
    console.log("Requesting auth token from the page", tab.id);
    chrome.tabs.sendMessage(tab.id, {
      action: "REQUEST_AUTH_TOKEN",
    });
    chrome.tabs.sendMessage(tab.id, {
      action: "EXTENSION_INSTALLED",
    });
  }
}

function captureVisibleTab(): Promise<string> {
  return new Promise((resolve) => {
    chrome.tabs.captureVisibleTab(null, { format: "png" }, (dataUrl) => {
      resolve(dataUrl);
    });
  });
}

function cropScreenshot(
  screenshot: string,
  bounds: { x: number; y: number; width: number; height: number }
): Promise<string> {
  return new Promise((resolve) => {
    const blobUrl = screenshot.replace(/^data:image\/(png|jpg);base64,/, "");
    const byteCharacters = atob(blobUrl);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);
      const byteNumbers = new Array(slice.length);

      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: "image/png" });

    createImageBitmap(blob).then((bitmap) => {
      const canvas = new OffscreenCanvas(bounds.width, bounds.height);
      const ctx = canvas.getContext("2d");

      if (ctx) {
        ctx.drawImage(
          bitmap,
          bounds.x,
          bounds.y,
          bounds.width,
          bounds.height,
          0,
          0,
          bounds.width,
          bounds.height
        );

        canvas.convertToBlob().then((croppedBlob) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            resolve(reader.result as string);
            bitmap.close();
          };
          reader.readAsDataURL(croppedBlob);
        });
      }
    });
  });
}

// Handler for capturing a screenshot of the current tab
async function captureScreenshot(): Promise<string | null> {
  try {
    // Get the active tab in the current window
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      console.error("No active tab found");
      return null;
    }

    // Capture a screenshot of the visible tab
    const dataUrl = await chrome.tabs.captureVisibleTab(null, {
      format: "png",
      quality: 70, // Reduce quality to save space
    });

    return dataUrl;
  } catch (error) {
    console.error("Error capturing screenshot:", error);
    return null;
  }
}

// Modify the existing message listener to handle auth checks
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "captureTab") {
    captureVisibleTab().then((screenshot) => {
      sendResponse(screenshot);
    });
    return true;
  }

  if (request.action === "toggleRecording") {
    chrome.sidePanel.setOptions({
      enabled: true,
      path: "tabs/sidepanel.html",
    });
    chrome.sidePanel.open({ windowId: sender.tab.windowId }, () => {
      if (chrome.runtime.lastError) {
        console.error("Error opening side panel:", chrome.runtime.lastError);
      }
    });
    guideManager.isRecording().then((isRecording) => {
      if (!isRecording) {
        guideManager.startRecording();
      } else {
        guideManager.stopRecording();
        sendResponse({ isRecording });
      }
    });
  } else if (request.action === "addStep") {
    chrome.sidePanel.setOptions({
      enabled: true,
      path: "tabs/sidepanel.html",
    });
    chrome.sidePanel.open({ windowId: sender.tab.windowId }, () => {
      if (chrome.runtime.lastError) {
        console.error("Error opening side panel:", chrome.runtime.lastError);
      }
    });
    if (request.step.action === "input") {
      guideManager.addInputStep(request.step);
    } else {
      guideManager.addStep(request.step);
    }
  } else if (request.action === "addInputStep") {
    console.log("Adding input step");
    chrome.sidePanel.setOptions({
      enabled: true,
    });
    chrome.sidePanel.open({ windowId: sender.tab.windowId }, () => {
      if (chrome.runtime.lastError) {
        console.error("Error opening side panel:", chrome.runtime.lastError);
      }
    });
    guideManager.addInputStep(request.step);
  } else if (request.action === "getSessions") {
    guideManager.getCurrentGuide().then((response) => {
      sendResponse(response);
    });
    return true;
  } else if (request.action === "exportGuideAsJSON") {
    guideManager
      .exportGuideAsJSON(request.title)
      .then((guideJSON) => {
        sendResponse({ guideJSON });
      })
      .catch((error) => {
        console.error("Error exporting guide as JSON:", error);
        sendResponse({ error: error.message });
      });
    return true;
  } else if (request.action === "captureKnowledge") {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: "startCapture",
        pinId: request.pinId, // Pass the pin ID to the content script
      });
    });
  } else if (request.action === "performCapture") {
    const { bounds, pinId } = request;
    captureVisibleTab().then(async (screenshot) => {
      // Crop the screenshot before sending to knowledge capture
      const croppedScreenshot = await cropScreenshot(screenshot, bounds);
      chrome.tabs.sendMessage(sender.tab.id, {
        action: "openKnowledgeCapture",
        screenshot: croppedScreenshot,
        currentUrl: sender.tab?.url,
        pinId: pinId, // Pass the pinId if it exists
      });
    });
  } else if (request.action === "initiateHoustonChat") {
    houstonManager.toggleChatVisibility(sender.tab?.url);
  } else if (request.action === "fetchChatHistory") {
    houstonManager
      .fetchChatHistory()
      .then((history) => {
        sendResponse({ success: true, history });
      })
      .catch((error) => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  } else if (request.action === "fetchChatContent") {
    houstonManager
      .fetchChatContent(request.chatId)
      .then((content) => {
        sendResponse({
          success: true,
          content: content.messages,
          sources: content.sources,
        });
      })
      .catch((error) => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  } else if (request.action === "toggleToolbar") {
    isToolbarVisible = request.show;
    inject(sender.tab.id);
  } else if (request.action === "saveKnowledgeCapture") {
    // Handle knowledge capture save
    handleKnowledgeCaptureSave(request.data, sendResponse);
    return true; // Required for async response
  } else if (request.action === "openTalknician") {
    // Use urlPattern with wildcard to match the domain
    if (request.url === "https://app.talknician.com") {
      chrome.tabs.create({ url: FE_URL, active: true });
      sendResponse({ success: true });
      return;
    }
    const urlPattern = `${FE_URL}/*`;
    try {
      chrome.tabs.query({ url: urlPattern }, (tabs) => {
        if (chrome.runtime.lastError) {
          console.error(chrome.runtime.lastError);
          // Fallback: just create a new tab
          chrome.tabs.create({ url: request.url, active: true });
          sendResponse({ success: true });
          return;
        }

        if (tabs && tabs.length > 0) {
          // Tab exists, activate it and update URL
          chrome.tabs.update(
            tabs[0].id,
            { active: true, url: request.url },
            () => {
              if (chrome.runtime.lastError) {
                console.error(chrome.runtime.lastError);
                return;
              }
              chrome.windows.update(tabs[0].windowId, { focused: true });
            }
          );
        } else {
          // Create new tab and activate it
          chrome.tabs.create({ url: request.url, active: true });
        }
        sendResponse({ success: true });
      });
    } catch (error) {
      console.error("Error handling tab operation:", error);
      // Fallback: create new tab
      chrome.tabs.create({ url: request.url, active: true });
      sendResponse({ success: true });
    }
    return true; // Required for async response
  } else if (request.action === "sendMessage") {
    houstonManager
      .sendMessage(request.query, request.internetSearch)
      .then((response) => {
        sendResponse({ success: true, response });
      })
      .catch((error) => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  } else if (request.action === "openKnowledgeCapture") {
    console.log("Handling openKnowledgeCapture action", {
      pinId: request.pinId,
    });
    chrome.tabs.sendMessage(sender.tab.id, {
      action: "openKnowledgeCapture",
      screenshot: request.screenshot,
      currentUrl: sender.tab?.url,
      pinId: request.pinId, // Pass the pinId if it exists
    });
  } else if (request.action === "TOGGLE_PIN_MODE") {
    handleTogglePinMode(request.active, sender.tab?.id)
      .then((response) => {
        sendResponse(response);
      })
      .catch((error) => {
        console.error("Error toggling pin mode:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep the message channel open for async response
  } else if (request.action === "TOGGLE_PINS_VISIBILITY") {
    handleTogglePinsVisibility(request.hidden, sender.tab?.id)
      .then((response) => {
        sendResponse(response);
      })
      .catch((error) => {
        console.error("Error toggling pins visibility:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep the message channel open for async response
  } else if (request.action === "openPinDetailsWindow") {
    // Open a new window with the pin details page
    const pinDetailsUrl = chrome.runtime.getURL(
      `tabs/pin-details.html?pinData=${request.pinData}`
    );

    // Add pin ID to URL if needed
    const urlWithParams = new URL(pinDetailsUrl);
    if (request.pinId) {
      urlWithParams.searchParams.append("pinId", request.pinId);
    }

    chrome.sidePanel.setOptions({
      path: urlWithParams.toString(),
      enabled: true,
    });

    chrome.sidePanel.open({ windowId: sender.tab.windowId }, () => {
      if (chrome.runtime.lastError) {
        console.error("Error opening side panel:", chrome.runtime.lastError);
      }
    });
    return true; // Keep the message channel open for async response
  } else if (request.action === "captureScreenshot") {
    captureScreenshot()
      .then((dataUrl) => {
        sendResponse({ success: true, dataUrl });
      })
      .catch((error) => {
        console.error("Error in captureScreenshot:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate we will send a response asynchronously
  } else if (request.action === "pinDeleted") {
    // Forward pin deletion notification to all tabs
    console.log("Received pin deletion notification, forwarding to all tabs");
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach((tab) => {
        if (tab.id) {
          try {
            chrome.tabs.sendMessage(tab.id, {
              action: "pinDeleted",
              pinId: request.pinId,
              url: request.url,
            });
          } catch (error) {
            // Ignore errors from tabs that don't have the content script
            console.log(`Could not send to tab ${tab.id}:`, error);
          }
        }
      });
    });
    sendResponse({ success: true });
    return true;
  } else if (request.action === "pinUpdated") {
    // Forward pin update notification to all tabs
    console.log("Received pin update notification, forwarding to all tabs");
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach((tab) => {
        if (tab.id) {
          try {
            chrome.tabs.sendMessage(tab.id, {
              action: "pinUpdated",
              pin: request.pin,
            });
          } catch (error) {
            // Ignore errors from tabs that don't have the content script
            console.log(`Could not send to tab ${tab.id}:`, error);
          }
        }
      });
    });
    sendResponse({ success: true });
    return true;
  } else if (request.action === "linkKnowledgeToPin") {
    // Handle linking a knowledge capture to a pin
    handleLinkKnowledgeToPin(request.pinId, request.knowledgeId)
      .then((response) => {
        sendResponse(response);
      })
      .catch((error) => {
        console.error("Error linking knowledge to pin:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep the message channel open for async response
  } else if (request.action === "toggleUIVisibility") {
    // Forward to recording manager and handle response
    recordingManager.handleToggleUIVisibility(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "showUI") {
    // Forward to recording manager and handle response
    recordingManager.handleShowUI(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "hideUI") {
    // Forward to recording manager and handle response
    recordingManager.handleHideUI(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "startRecording") {
    // Forward to recording manager and handle response
    recordingManager.handleStartRecording(request, sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "pauseRecording") {
    // Forward to recording manager and handle response
    recordingManager.handlePauseRecording(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "resumeRecording") {
    // Forward to recording manager and handle response
    recordingManager.handleResumeRecording(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "stopRecording") {
    // Forward to recording manager and handle response
    recordingManager.handleStopRecording(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "resetRecording") {
    // Forward to recording manager and handle response
    recordingManager.handleResetRecording(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "processRecording") {
    // Handle the recorded blob
    handleProcessRecording(request, sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "uploadRecording") {
    // Forward to recording manager and handle response
    recordingManager.handleUploadRecording(request, sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "cancelRecording") {
    // Forward to recording manager and handle response
    recordingManager.handleCancelRecording(sendResponse);
    return true; // Keep the message channel open for async response
  } else if (request.action === "recordingActuallyStarted") {
    // Notify the recording manager that recording has actually started
    recordingManager.handleRecordingActuallyStarted();
    if (sendResponse) sendResponse({ success: true });
    return true; // Keep the message channel open for async response
  } else if (request.action === "recordingStoppedFromPage") {
    // Notify the recording manager that recording was stopped from the page
    recordingManager.handleRecordingStoppedFromPage();
    if (sendResponse) sendResponse({ success: true });
    return true; // Keep the message channel open for async response
  } else if (request.action === "VIDEO_PROCESSING_STARTED") {
    // Forward the video processing started event to any open Talknician tabs
    console.log("Received VIDEO_PROCESSING_STARTED event:", request);

    // Get the website URL pattern to find Talknician tabs
    getWebsiteUrl().then((websiteUrl) => {
      const urlPattern = `${websiteUrl}/*`;

      // Find all Talknician tabs
      chrome.tabs.query({ url: urlPattern }, (tabs) => {
        if (tabs && tabs.length > 0) {
          console.log(
            `Found ${tabs.length} Talknician tabs to notify about video processing`
          );

          // Forward the message to each tab
          tabs.forEach((tab) => {
            if (tab.id) {
              try {
                chrome.tabs.sendMessage(tab.id, {
                  action: "VIDEO_PROCESSING_STARTED",
                  process_id: request.process_id,
                  message: request.message || "Video processing started",
                });
                console.log(`Sent VIDEO_PROCESSING_STARTED to tab ${tab.id}`);
              } catch (error) {
                console.log(`Could not send to tab ${tab.id}:`, error);
              }
            }
          });
        } else {
          console.log(
            "No Talknician tabs found to notify about video processing"
          );
        }
      });
    });

    if (sendResponse) sendResponse({ success: true });
    return true; // Keep the message channel open for async response
  } else if (request.action === "openRecordingPage") {
    // Open the recording page in a new tab
    chrome.tabs.create(
      {
        url: chrome.runtime.getURL("tabs/recording-page.html"),
        pinned: true,
      },
      (tab) => {
        if (chrome.runtime.lastError) {
          console.error(
            "Error opening recording page:",
            chrome.runtime.lastError
          );
          if (sendResponse) {
            sendResponse({
              success: false,
              error: chrome.runtime.lastError.message,
            });
          }
        } else {
          if (sendResponse) {
            sendResponse({ success: true, tabId: tab.id });
          }
        }
      }
    );
    return true; // Keep the message channel open for async response
  }

  return true;
});

const gimmeToggle = (toggleIn) => {
  toggleIt = toggleIn;
  chrome.action.onClicked.addListener(async (tab) => {
    toggleIt(tab);
  });
};

chrome.contextMenus.create({
  id: "launcher",
  title: "Show/Hide",
  contexts: ["all"],
});

chrome.contextMenus.onClicked.addListener(async ({ menuItemId }, tab) => {
  if (menuItemId === "launcher") {
    toggleIt(tab);
  }
});

const toggleIn = async ({ id: tab_id }) => {
  // toggle out: it's currently loaded and injected
  const isAuthenticated = await checkAuthentication();
  if (!isAuthenticated) {
    return;
  }
  if (state.loaded[tab_id] && state.injected[tab_id]) {
    chrome.tabs.query({ active: true, currentWindow: true }).then(() => {
      chrome.scripting.executeScript({
        target: { tabId: tab_id },
        files: [ejectJS.split("/").pop().split("?")[0]],
      });
    });

    state.injected[tab_id] = false;
    isToolbarVisible = false;
  }
  // toggle in: it's loaded and needs injected
  else if (state.loaded[tab_id] && !state.injected[tab_id]) {
    if (!isAuthenticated && isToolbarVisible) {
      return;
    }
    isToolbarVisible = !isToolbarVisible;
    inject(tab_id);
  }
  // fresh start in tab
  else {
    if (!isAuthenticated && isToolbarVisible) {
      return;
    }
    chrome.tabs.query({ active: true, currentWindow: true }).then(() => {
      chrome.scripting.executeScript({
        target: { tabId: tab_id },
        files: [injectJS.split("/").pop().split("?")[0]],
      });
    });

    state.loaded[tab_id] = true;
    state.injected[tab_id] = true;
    isToolbarVisible = true;
  }

  chrome.tabs.onUpdated.addListener(function (tabId) {
    if (tabId === tab_id) state.loaded[tabId] = false;
  });
};

gimmeToggle(toggleIn);

const inject = async (tabId: number) => {
  chrome.scripting.executeScript({
    target: { tabId },
    world: "MAIN",
    func: toggleToolbar,
    args: [isToolbarVisible],
  });
};

// Handle context menu toggle
chrome.contextMenus.create({
  id: "toggleToolbar",
  title: "Show/Hide Toolbar",
  contexts: ["all"],
});

async function handleKnowledgeCaptureSave(
  data: any,
  sendResponse: (response: any) => void
) {
  try {
    // Upload image and get URL
    const imageUrl = await uploadImage(data.screenshot);

    // Update the capture data with the new image URL
    const updatedData = {
      ...data,
      screenshot: imageUrl,
    };

    // TODO: save to db
    // Send back the updated data with success status
    sendResponse({ success: true, data: updatedData });
  } catch (error) {
    console.error("Error processing knowledge capture:", error);
    sendResponse({ success: false, error: error.message });
  }
}

// Function to handle processing the recorded video
async function handleProcessRecording(
  request: any,
  sendResponse: (response: any) => void
) {
  try {
    console.log("Processing recording...", request);

    // Check if we have a blob
    if (!request.blob) {
      console.error("No blob received in processRecording request");
      sendResponse({
        success: false,
        error: "No recording data received. Please try again.",
      });
      return;
    }

    // For debugging, log the blob size and type
    console.log("Blob size:", request.blob.size, "bytes");
    console.log("Blob type:", request.blob.type);

    // Check if the recording has audio (but make this optional)
    if (!request.hasAudio) {
      console.warn("Recording has no audio tracks - continuing anyway");
      // We'll continue processing but warn the user
    }

    // Convert blob to File object
    const blob = request.blob;
    const fileName = `recording_${Date.now()}.webm`;

    // Create a File object from the blob
    const file = new File([blob], fileName, { type: "video/webm" });
    console.log("Created file:", fileName, "size:", file.size);

    // Create a URL for the blob that can be used in the UI
    const blobUrl = URL.createObjectURL(blob);
    console.log("Created blob URL:", blobUrl);

    // TODO: Upload the file to the server
    // For now, just return success with the blob URL
    console.log("Recording processed successfully");
    sendResponse({
      success: true,
      fileName,
      fileSize: file.size,
      blobUrl,
      hasAudio: request.hasAudio,
      message: "Recording processed successfully",
    });
  } catch (error) {
    console.error("Error processing recording:", error);
    sendResponse({ success: false, error: error.message });
  }
}

const getUrlPattern = async () => {
  const websiteUrl = await getWebsiteUrl();
  return `${websiteUrl}/*`;
};

// Update the externally_connectable setup
chrome.runtime.onMessageExternal.addListener(
  async (request, sender, sendResponse) => {
    const urlPattern = await getUrlPattern();
    if (!sender.url?.match(urlPattern)) {
      return;
    }
    // ... rest of the listener code ...
  }
);

// Add this to your background script

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "getCurrentTabId" && sender.tab) {
    sendResponse({ tabId: sender.tab.id });
  }
  return true; // Keep the message channel open for async responses
});

const handleTogglePinMode = async (active: boolean, tabId: number) => {
  try {
    if (!tabId) {
      throw new Error("Tab ID is required");
    }

    // Inject the pin content script if it's not already injected
    try {
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ["src/contents/pin-content.js"], // Note: TypeScript files are compiled to JS
      });
    } catch (error) {
      console.warn("Pin content script might already be injected:", error);
    }

    // Send the toggle message to the content script
    await chrome.tabs.sendMessage(tabId, {
      action: "TOGGLE_PINNING_MODE",
      active,
    });

    return { success: true };
  } catch (error) {
    console.error("Error handling pin mode toggle:", error);
    return { success: false, error: error.message };
  }
};

// Function to handle toggling pins visibility
const handleTogglePinsVisibility = async (hidden: boolean, tabId: number) => {
  try {
    if (!tabId) {
      throw new Error("Tab ID is required");
    }

    // Inject the pin content script if it's not already injected
    try {
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ["src/contents/pin-content.js"], // Note: TypeScript files are compiled to JS
      });
    } catch (error) {
      console.warn("Pin content script might already be injected:", error);
    }

    // Send the toggle visibility message to the content script
    await chrome.tabs.sendMessage(tabId, {
      action: "TOGGLE_PINS_VISIBILITY",
      hidden,
    });¨

    return { success: true };
  } catch (error) {
    console.error("Error handling pins visibility toggle:", error);
    return { success: false, error: error.message };
  }
};
