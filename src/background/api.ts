import { Storage } from "@plasmohq/storage";
import {
  checkAuthentication,
  getAuthToken,
  redirectToLogin,
} from "~utils/auth";
import type { Guide } from "./guide-manager";
import type { KnowledgeCapture } from "./knowledge-manager";
import type { ChatMessage } from "./houston-manager";
import { v4 as uuidv4 } from "uuid";
import { getApiUrl } from "~utils/domain-config";

const storage = new Storage();
let API_BASE_URL: string;

const initializeApiBaseUrl = async () => {
  API_BASE_URL = await getApiUrl();
};

// Initialize on module load
initializeApiBaseUrl();

interface ImageUploadResponse {
  email: string;
  filename: string;
  file_url: string;
}

async function base64ToBlob(base64String: string): Promise<Blob> {
  const response = await fetch(base64String);
  return await response.blob();
}

// Knowledge capture interfaces
export interface KnowledgeCaptureResponse {
  uuid: string;
  screenshot: string;
  description: string;
  is_favorite: boolean;
  creator_email: string;
  current_url: string;
  created_at: string;
  updated_at: string;
}

export interface GuideResponse {
  uuid: string;
  title: string;
  thumbnail: string;
  is_favorite: boolean;
  steps: GuideStep[];
  creator_email: string;
  author_name: string;
  created_at: string;
  updated_at: string;
}

export interface GuideStep {
  uuid: string;
  order: number;
  description: string;
  action: string;
  element: string;
  text: string;
  image: string;
  screenshot: string;
  current_url: string;
  querySelector: string;
  callouts: any[];
  comments: any[];
  is_deleted: boolean;
}

// Function to fetch all knowledge captures
export async function fetchAllKnowledgeCaptures(): Promise<
  KnowledgeCaptureResponse[]
> {
  try {
    return fetchWithAuth<KnowledgeCaptureResponse[]>(
      "/knowledge_capture/quick-captures/",
      {
        method: "GET",
      }
    );
  } catch (error) {
    console.error("Error fetching knowledge captures:", error);
    return [];
  }
}

// Function to fetch all guides
export async function fetchAllGuides(): Promise<GuideResponse[]> {
  try {
    return fetchWithAuth<GuideResponse[]>("/knowledge_capture/guides/", {
      method: "GET",
    });
  } catch (error) {
    console.error("Error fetching guides:", error);
    return [];
  }
}

export async function fetchWithAuth<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    // Check authentication first
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
      throw new Error("Authentication required");
    }

    const token = await getAuthToken();
    const url = endpoint.startsWith("http")
      ? endpoint
      : `${API_BASE_URL}${endpoint}`;

    const headers: HeadersInit = {
      Authorization: `Bearer ${token}`,
      ...options.headers,
    };

    if (!(options.body instanceof FormData)) {
      headers["Content-Type"] = "application/json";
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      // If unauthorized, redirect to login
      if (response.status === 401) {
        console.error("API call failed:", 401);
        //Todo: implement verification of token and delete it if it is expired
        await storage.remove("bearerToken");
        redirectToLogin();
        throw new Error("Authentication failed");
      }
      throw new Error(`API call failed with status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error("API call failed:", error);
    throw error;
  }
}

export async function uploadImage(
  imageFile: File | Blob | string
): Promise<string> {
  let blob: Blob;

  if (typeof imageFile === "string") {
    blob = await base64ToBlob(imageFile);
  } else {
    blob = imageFile;
  }

  const filename = `screenshot_${uuidv4()}.png`;

  const formData = new FormData();
  formData.append("file", blob, filename);

  const data = await fetchWithAuth<ImageUploadResponse>(
    "/documents/image-upload/",
    {
      method: "POST",
      body: formData,
    }
  );

  return data.file_url;
}

export async function saveKnowledgeCaptureGuide(
  captureData: Guide
): Promise<any> {
  return fetchWithAuth("/knowledge_capture/guides/", {
    method: "POST",
    body: JSON.stringify(captureData),
  });
}

export async function saveKnowledgeCaptureQuickCapture(
  captureData: KnowledgeCapture
): Promise<any> {
  return fetchWithAuth("/knowledge_capture/quick-captures/", {
    method: "POST",
    body: JSON.stringify(captureData),
  });
}

export async function fetchIsValidToken(): Promise<boolean> {
  try {
    const response = await fetchWithAuth<{ valid: boolean; message: string }>(
      "/authentication/verify",
      {
        method: "GET",
      }
    );
    return response.valid;
  } catch (error) {
    console.error("Error fetching valid token:", error);
    return false;
  }
}

export interface HoustonResponse {
  chat_id: string;
  houston_response: string;
  chat_history: ChatMessage[];
  local_document_text_sources: any[];
  local_images: {
    source_url: string;
    filename: string;
    image_number: number;
    image_url: string;
    relevance_score: number;
  }[];
  user_guide_results: any[];
  suggested_questions: string[];
  internet_results: {
    webpages: any[];
    videos: any[];
    images: any[];
  };
}

export async function startNewChat(query: string): Promise<HoustonResponse> {
  return fetchWithAuth<HoustonResponse>("/houston/", {
    method: "POST",
    body: JSON.stringify({
      query,
      internet_search: 1,
    }),
  });
}

export async function sendChatMessage(
  chatId: string,
  query: string,
  internetSearch: boolean = true
): Promise<HoustonResponse> {
  return fetchWithAuth<HoustonResponse>("/houston/", {
    method: "POST",
    body: JSON.stringify({
      chat_id: chatId,
      query,
      internet_search: internetSearch ? 1 : 0,
    }),
  });
}

export async function fetchChatHistory() {
  const response = await fetchWithAuth<
    {
      chat_id: string;
      created_at: string;
    }[]
  >("/houston/");

  // Sort by created_at in descending order (latest first)
  return response
    .sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
    .map((chat) => ({
      text: `Chat from ${new Date(chat.created_at).toLocaleString()}`,
      chat_id: chat.chat_id,
      created_at: chat.created_at, // Keep the created_at for potential UI enhancements
    }));
}

export async function fetchChatContent(chatId: string) {
  const response = await fetchWithAuth<ChatMessage[]>(
    `/houston/?chat_id=${chatId}`
  );
  return response;
}

export interface FetchCurrentUserProfileResponse {
  email: string;
  name: string;
  role: string;
  profile_picture: string;
  organization: {
    organization_name: string;
    manager_email: string;
    organization_logo: string;
  };
  created_at: string | null;
}

export async function fetchCurrentUserProfile(): Promise<FetchCurrentUserProfileResponse> {
  const response = await fetchWithAuth<FetchCurrentUserProfileResponse>(
    `/organization/profile/`
  );
  return response;
}

export async function uploadVideo(videoFile: File): Promise<string> {
  const formData = new FormData();
  formData.append("video_file", videoFile);
  const response = await fetchWithAuth<{ process_id: string }>(
    "/video-guides/",
    {
      method: "POST",
      body: formData,
    }
  );
  return response.process_id;
}

export interface VideoGuideStep {
  step: number;
  title: string;
  content: string;
  startTime: string;
  endTime: string;
  summary: string;
}

export interface VideoGuideResponse {
  id: number;
  user_email: string;
  srt_url: string;
  video: string;
  response: {
    steps: VideoGuideStep[];
  };
  title: string;
  thumbnail_image: string;
  edit_access: boolean;
  delete_access: boolean;
}

export async function fetchAllVideoGuides(): Promise<VideoGuideResponse[]> {
  try {
    return fetchWithAuth<VideoGuideResponse[]>("/video-guides/", {
      method: "GET",
    });
  } catch (error) {
    console.error("Error fetching video guides:", error);
    return [];
  }
}

// Pin interface matching the API response
export interface PinResponse {
  id: number;
  uuid: string;
  xPercent?: number;
  yPercent?: number;
  description: string;
  timestamp: number;
  url: string;
  isCoordinateBased: boolean;
  pageTitle?: string;
  browser?: string;
  os?: string;
  resolution?: string;
  browserWindow?: string;
  colorDepth?: string;
  clientTimestamp?: string;
  screenshot?: string;
  selector?: string;
  linkedGuides: string[];
  linkedKnowledge: string[];
  creator_email: string;
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
}

// Function to fetch all pins
export async function fetchAllPins(): Promise<PinResponse[]> {
  try {
    return fetchWithAuth<PinResponse[]>("/knowledge_capture/pins/", {
      method: "GET",
    });
  } catch (error) {
    console.error("Error fetching pins:", error);
    return [];
  }
}

// Function to fetch a specific pin by UUID
export async function fetchPin(uuid: string): Promise<PinResponse> {
  try {
    const response = await fetchWithAuth<PinResponse>(
      `/knowledge_capture/pins/${uuid}/`,
      {
        method: "GET",
      }
    );
    console.log(`Successfully fetched pin with UUID ${uuid}:`, response);
    return response;
  } catch (error) {
    console.error(`Error fetching pin with UUID ${uuid}:`, error);
    throw error;
  }
}

// Function to create a new pin
export async function createPin(pinData: any): Promise<PinResponse> {
  try {
    return fetchWithAuth<PinResponse>("/knowledge_capture/pins/", {
      method: "POST",
      body: JSON.stringify(pinData),
    });
  } catch (error) {
    console.error("Error creating pin:", error);
    throw error;
  }
}

// Function to update a pin
export async function updatePin(
  uuid: string,
  pinData: any
): Promise<PinResponse> {
  try {
    console.log(`Updating pin with UUID ${uuid}:`, pinData);
    const response = await fetchWithAuth<PinResponse>(
      `/knowledge_capture/pins/${uuid}/`,
      {
        method: "PUT",
        body: JSON.stringify(pinData),
      }
    );
    console.log(`Successfully updated pin with UUID ${uuid}:`, response);
    return response;
  } catch (error) {
    console.error(`Error updating pin with UUID ${uuid}:`, error);
    throw error;
  }
}

// Function to delete a pin
export async function deletePin(uuid: string): Promise<{ message: string }> {
  try {
    return fetchWithAuth<{ message: string }>(
      `/knowledge_capture/pins/${uuid}/`,
      {
        method: "DELETE",
      }
    );
  } catch (error) {
    console.error(`Error deleting pin with UUID ${uuid}:`, error);
    throw error;
  }
}

// Function to delete a knowledge capture
export async function deleteKnowledgeCapture(
  uuid: string
): Promise<{ message: string }> {
  try {
    return fetchWithAuth<{ message: string }>(
      `/knowledge_capture/quick-captures/${uuid}/`,
      {
        method: "DELETE",
      }
    );
  } catch (error) {
    console.error(`Error deleting knowledge capture with UUID ${uuid}:`, error);
    throw error;
  }
}

// Function to delete multiple knowledge captures
export async function deleteMultipleKnowledgeCaptures(
  uuids: string[]
): Promise<{ message: string }> {
  try {
    const results = await Promise.allSettled(
      uuids.map((uuid) => deleteKnowledgeCapture(uuid))
    );

    const failures = results.filter((result) => result.status === "rejected");
    if (failures.length > 0) {
      console.error(
        `Failed to delete ${failures.length} out of ${uuids.length} knowledge captures`
      );
    }

    return {
      message: `Successfully deleted ${uuids.length - failures.length} out of ${uuids.length} knowledge captures`,
    };
  } catch (error) {
    console.error(`Error deleting multiple knowledge captures:`, error);
    throw error;
  }
}
