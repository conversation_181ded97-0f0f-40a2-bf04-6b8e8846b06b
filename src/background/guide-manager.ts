import { Storage } from "@plasmohq/storage";
import { saveKnowledgeCaptureGuide, uploadImage } from "./api";

export interface Step {
  order?: number;
  description?: string;
  action: string;
  element: string;
  text?: string;
  value?: string;
  timestamp: string;
  screenshot?: string;
  image?: string;
  querySelector?: string;
  current_url?: string;
}

export interface Guide {
  id: string;
  title: string;
  startTime: string;
  steps: Step[];
  endTime: string | null;
  uuid?: string;
}

class GuideManager {
  private storage: Storage;
  private currentGuide: Guide | null = null;
  private typingTimeout: NodeJS.Timeout | null = null;
  private currentInputStep: Step | null = null;

  constructor() {
    this.storage = new Storage({
      area: "local",
    });
    this.initializeStorage();
  }

  public getStorage() {
    return this.storage;
  }

  private async initializeStorage() {
    const storedCurrentGuide = (await this.storage.get(
      "currentGuide"
    )) as Guide | null;
    this.currentGuide = storedCurrentGuide;
  }

  private async updateStorage() {
    await this.storage.set("currentGuide", this.currentGuide);
  }

  public generateGuideId(): string {
    return `guide_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async startRecording() {
    // Clear any existing guide before starting a new one
    await this.clearCurrentGuide();

    this.currentGuide = {
      id: this.generateGuideId(),
      title: "Untitled Guide",
      startTime: new Date().toISOString(),
      steps: [],
      endTime: null,
    };
    await this.storage.set("is-recording", true);
    await this.updateStorage();
    return true;
  }

  async stopRecording() {
    if (!this.currentGuide) return false;

    this.currentGuide.endTime = new Date().toISOString();
    const completedGuide = this.currentGuide;
    await this.storage.set("is-recording", false);
    return completedGuide;
  }

  async addStep(step: Step) {
    if (!this.currentGuide) return false;

    // Let the sidepanel handle adding the step with screenshot
    chrome.runtime.sendMessage({ action: "addStep", step });
    return true;
  }

  private async uploadScreenshots(guide: Guide): Promise<Guide> {
    const uploadPromises = guide.steps.map(async (step) => {
      if (step.screenshot) {
        try {
          // Fetch the blob URL content
          const response = await fetch(step.screenshot);
          const blob = await response.blob();
          // Upload the image
          const imageUrl = await uploadImage(blob);
          // Revoke the blob URL since we don't need it anymore
          return { ...step, screenshot: imageUrl, image: imageUrl };
        } catch (error) {
          console.error("Failed to upload screenshot:", error);
          return step;
        }
      }
      return step;
    });

    const updatedSteps = await Promise.all(uploadPromises);
    return { ...guide, steps: updatedSteps };
  }

  async exportGuideAsJSON(title?: string): Promise<string> {
    const currentGuide = (await this.storage.get("currentGuide")) as Guide | null;
    if (!currentGuide) return "";

    // Upload all screenshots and get updated guide
    const processedGuide = await this.uploadScreenshots(currentGuide);
    const addOrder = processedGuide.steps.map((step, index) => ({
      ...step,
      order: index + 1,
    }));

    const finalGuide = {
      ...processedGuide,
      steps: addOrder,
      title: title || processedGuide.title,
      thumbnail: processedGuide.steps[0].image,
    };

    const savedGuide = await saveKnowledgeCaptureGuide(finalGuide);
    return savedGuide;
  }

  async clearCurrentGuide() {
    this.currentGuide = null;
    await this.updateStorage();
    await this.storage.set("is-recording", false);
  }

  async addInputStep(step: Step) {
    if (!this.currentGuide) return false;

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    console.log("Adding input step");
    // If there's an existing input step, update it instead of creating a new one
    if (this.currentInputStep && this.currentInputStep.element === step.element) {
      this.currentInputStep = step;
      // Send update message to update the current step
      chrome.runtime.sendMessage({ 
        action: "updateInputStep", 
        step,
        element: step.element 
      });
      return true;
    }

    // For a new input field, create a new step
    this.currentInputStep = step;
    await this.addStep(step);
    return true;
  }

  async updateInputStep(step: Step) {
    if (!this.currentGuide || !this.currentInputStep) return false;

    const lastStepIndex = this.currentGuide.steps.length - 1;
    if (lastStepIndex >= 0 && this.currentGuide.steps[lastStepIndex].element === step.element) {
      this.currentGuide.steps[lastStepIndex] = step;
      await this.updateStorage();
      return true;
    }

    return false;
  }

  async isRecording(): Promise<boolean> {
    const isRecording = await this.storage.get("is-recording");
    return Boolean(isRecording);
  }

  async getCurrentGuide(): Promise<{
    currentGuide: Guide | null;
    isRecording: boolean;
  }> {
    return {
      currentGuide: this.currentGuide,
      isRecording: await this.isRecording(),
    };
  }

  async updateGuideTitle(title: string) {
    if (!this.currentGuide) return false;
    this.currentGuide.title = title;
    await this.updateStorage();
    return true;
  }
}

export const guideManager = new GuideManager();
