// This function will be injected into the MAIN world
export default function toggleToolbar(show: boolean) {
  const toolbar = document.querySelector("#talknician-toolbar") as HTMLElement;
  if (toolbar) {
    if (show) {
      toolbar.style.display = "block";
      toolbar.animate(
        [
          { transform: "translateX(-200%)", opacity: 0 },
          { transform: "translateX(0)", opacity: 1 },
        ],
        {
          duration: 300,
          easing: "ease-out",
        }
      );
    } else {
      toolbar.animate(
        [
          { transform: "translateX(0)", opacity: 1 },
          { transform: "translateX(-200%)", opacity: 0 },
        ],
        {
          duration: 300,
          easing: "ease-out",
        }
      ).onfinish = () => {
        toolbar.style.display = "none";
      };
    }
  }
}
