import { Storage } from "@plasmohq/storage";
import {
  fetchChatContent,
  fetchChatHistory,
  startNewChat,
  sendChatMessage,
  type HoustonResponse,
} from "./api";

export interface ChatMessage {
  role: string;
  content: string;
}

export interface ChatHistory {
  chat_id: string;
  text: string;
  created_at: string;
}

interface ChatSources {
  local_document_text_sources: any[];
  internet_results: {
    webpages: any[];
    videos: any[];
    images: any[];
  } | null;
}

class HoustonManager {
  private storage: Storage;
  private openedOnUrl: string | null = null;

  constructor() {
    this.storage = new Storage();
    this.initializeStorage();
  }

  private async initializeStorage() {
    this.openedOnUrl = await this.storage.get("houston_chat_opened_url");
  }

  private getChatSourcesKey(chatId: string): string {
    return `chat_sources_${chatId}`;
  }

  async storeChatSources(chatId: string, sources: ChatSources) {
    const key = this.getChatSourcesKey(chatId);
    await this.storage.set(key, sources);
  }

  async getChatSources(chatId: string): Promise<ChatSources | null> {
    const key = this.getChatSourcesKey(chatId);
    return await this.storage.get(key) || null;
  }

  async startNewChat(query: string): Promise<HoustonResponse> {
    try {
      const response = await startNewChat(query);
      await this.storage.set("current_chat_id", response.chat_id);
      
      // Store sources for the new chat
      await this.storeChatSources(response.chat_id, {
        local_document_text_sources: response.local_document_text_sources,
        internet_results: response.internet_results
      });
      
      return response;
    } catch (error) {
      console.error("Error starting new chat:", error);
      throw error;
    }
  }

  async sendMessage(query: string, internetSearch: boolean = true): Promise<HoustonResponse> {
    try {
      const currentChatId = await this.getCurrentChatId();
      if (!currentChatId) {
        return this.startNewChat(query);
      }
      const response = await sendChatMessage(currentChatId, query, internetSearch);
      
      // Store updated sources
      await this.storeChatSources(currentChatId, {
        local_document_text_sources: response.local_document_text_sources,
        internet_results: response.internet_results
      });
      
      return response;
    } catch (error) {
      console.error("Error sending message:", error);
      throw error;
    }
  }

  async fetchChatHistory(): Promise<ChatHistory[]> {
    try {
      return await fetchChatHistory();
    } catch (error) {
      console.error("Error fetching chat history:", error);
      throw error;
    }
  }

  async fetchChatContent(chatId: string): Promise<{messages: ChatMessage[], sources: ChatSources | null}> {
    try {
      const messages = await fetchChatContent(chatId);
      const sources = await this.getChatSources(chatId);
      return { messages, sources };
    } catch (error) {
      console.error("Error fetching chat content:", error);
      throw error;
    }
  }

  async toggleChatVisibility(currentUrl?: string) {
    const visible = await this.storage.get("houston_chat_visible");
    const newVisibility = !visible;
    
    if (newVisibility && currentUrl) {
      // Store the URL where the chat was opened
      this.openedOnUrl = currentUrl;
      await this.storage.set("houston_chat_opened_url", this.openedOnUrl);
    } else {
      // Clear the stored URL when closing
      this.openedOnUrl = null;
      await this.storage.remove("houston_chat_opened_url");
    }
    
    await this.storage.set("houston_chat_visible", newVisibility);
    return newVisibility;
  }

  async checkVisibility(currentUrl: string): Promise<boolean> {
    const isVisible = await this.storage.get("houston_chat_visible");
    if (isVisible && this.openedOnUrl !== currentUrl) {
      await this.setChatVisibility(false);
      return false;
    }
    return Boolean(isVisible);
  }

  async getChatVisibility(): Promise<boolean> {
    return Boolean(await this.storage.get("houston_chat_visible"));
  }

  async setChatVisibility(visible: boolean) {
    if (!visible) {
      this.openedOnUrl = null;
      await this.storage.remove("houston_chat_opened_url");
    }
    await this.storage.set("houston_chat_visible", visible);
  }

  async getCurrentChatId(): Promise<string | null> {
    return await this.storage.get("current_chat_id");
  }

  async setCurrentChatId(chatId: string | null) {
    if (chatId) {
      await this.storage.set("current_chat_id", chatId);
    } else {
      await this.storage.remove("current_chat_id");
    }
  }
}

export const houstonManager = new HoustonManager();
