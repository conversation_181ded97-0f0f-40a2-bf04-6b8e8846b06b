import { Storage } from "@plasmohq/storage";
import { v4 as uuidv4 } from "uuid";

export interface KnowledgeCapture {
  id: string;
  screenshot: string;
  description: string;
  shapes: any[];
  createdAt: string;
  current_url?: string;
  tabId?: number;
}

class KnowledgeManager {
  private storage: Storage;
  private currentCapture: KnowledgeCapture | null = null;
  private openedOnUrl: string | null = null;
  private activeTabId: number | null = null;

  constructor() {
    this.storage = new Storage({
      area: "local",
    });
    this.initializeStorage();
  }

  private async initializeStorage() {
    const storedCapture = (await this.storage.get(
      "current_capture"
    )) as KnowledgeCapture | null;
    this.currentCapture = storedCapture;
    this.openedOnUrl = await this.storage.get("knowledge_capture_opened_url");
    this.activeTabId =
      (await this.storage.get("knowledge_capture_tab_id")) || null;
  }

  private async updateStorage() {
    await this.storage.set("current_capture", this.currentCapture);
  }

  private generateCaptureId(): string {
    return uuidv4();
  }

  async startCapture(screenshot: string, currentUrl?: string, tabId?: number) {
    //convert to blob url at this point
    const response = await fetch(screenshot);
    const blob = await response.blob();
    const blobUrl = URL.createObjectURL(blob);

    // Clear any existing capture
    await this.clearCurrentCapture();

    this.currentCapture = {
      id: this.generateCaptureId(),
      screenshot: blobUrl,
      description: "",
      shapes: [],
      createdAt: new Date().toISOString(),
      tabId: tabId || null,
    };

    // Store the URL and tab ID where the capture was opened
    if (currentUrl) {
      this.openedOnUrl = currentUrl;
      await this.storage.set("knowledge_capture_opened_url", currentUrl);
    }

    if (tabId) {
      this.activeTabId = tabId;
      await this.storage.set("knowledge_capture_tab_id", tabId);
    }

    await this.storage.set("knowledge_capture_visible", true);
    await this.updateStorage();
    return true;
  }

  async updateCapture(updates: Partial<KnowledgeCapture>) {
    if (!this.currentCapture) return false;

    this.currentCapture = {
      ...this.currentCapture,
      ...updates,
    };

    await this.updateStorage();
    return true;
  }

  // async saveCapture(): Promise<KnowledgeCapture | null> {
  //   if (!this.currentCapture) return null;

  //   try {
  //     // Upload the screenshot
  //     const response = await fetch(this.currentCapture.screenshot);
  //     const blob = await response.blob();
  //     const imageUrl = await uploadImage(blob);

  //     const completedCapture: KnowledgeCapture = {
  //       ...this.currentCapture,
  //       screenshot: imageUrl,
  //       current_url: window.location.href,
  //     };

  //     await saveKnowledgeCaptureQuickCapture(completedCapture);
  //     // Clear the current capture after successful save
  //     await this.clearCurrentCapture();

  //     return completedCapture;
  //   } catch (error) {
  //     this.clearCurrentCapture();
  //     this.openedOnUrl = null;
  //     console.error("Failed to save capture:", error);
  //     throw error;
  //   }
  // }

  async checkVisibility(currentTabId: number): Promise<boolean> {
    const isVisible = await this.storage.get("knowledge_capture_visible");
    const storedTabId = await this.storage.get("knowledge_capture_tab_id");

    // Only show the capture on the tab where it was created
    if (storedTabId && storedTabId !== currentTabId.toString()) {
      return false;
    }

    return Boolean(isVisible);
  }

  async clearCurrentCapture() {
    this.currentCapture = null;
    this.openedOnUrl = null;
    this.activeTabId = null;
    await this.updateStorage();
    await this.storage.set("knowledge_capture_visible", false);
    await this.storage.remove("quick_capture_screenshot");
    await this.storage.remove("knowledge_capture_opened_url");
    await this.storage.remove("knowledge_capture_tab_id");
  }

  async getCurrentCapture(): Promise<{
    currentCapture: KnowledgeCapture | null;
    isVisible: boolean;
  }> {
    const isVisible = Boolean(
      await this.storage.get("knowledge_capture_visible")
    );

    return {
      currentCapture: this.currentCapture,
      isVisible,
    };
  }

  getStorage() {
    return this.storage;
  }
}

export const knowledgeManager = new KnowledgeManager();
