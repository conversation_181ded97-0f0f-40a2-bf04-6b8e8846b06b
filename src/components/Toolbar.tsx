import React from "react";
import type { Tool } from "../types";
import {
  CursorIcon,
  RectangleIcon,
  CircleIcon,
  LineIcon,
  ArrowIcon,
  TextIcon,
  UndoIcon,
  RedoIcon,
  BlurIcon,
} from "./icons";
import { ColorPicker } from "./ColorPicker";

interface ToolbarProps {
  selectedTool: Tool;
  onToolSelect: (tool: Tool) => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  onDelete: () => void;
  currentColor: string;
  onColorChange: (color: string) => void;
}

const Toolbar: React.FC<ToolbarProps> = ({
  selectedTool,
  onToolSelect,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  onDelete,
  currentColor,
  onColorChange,
}) => {
  return (
    <div className="toolbar">
      <div className="toolbar-content">
        <div className="tool-group">
          <ColorPicker color={currentColor} onChange={onColorChange} />
          <button
            className={`tool-button ${selectedTool === "cursor" ? "active" : ""}`}
            onClick={() => onToolSelect("cursor")}
          >
            <CursorIcon className="w-4 h-4" />
          </button>
          <button
            className={`tool-button ${selectedTool === "rectangle" ? "active" : ""}`}
            onClick={() => onToolSelect("rectangle")}
          >
            <RectangleIcon className="w-4 h-4" />
          </button>
          <button
            className={`tool-button ${selectedTool === "circle" ? "active" : ""}`}
            onClick={() => onToolSelect("circle")}
          >
            <CircleIcon className="w-4 h-4" />
          </button>
          <button
            className={`tool-button ${selectedTool === "line" ? "active" : ""}`}
            onClick={() => onToolSelect("line")}
          >
            <LineIcon className="w-4 h-4" />
          </button>
          <button
            className={`tool-button ${selectedTool === "arrow" ? "active" : ""}`}
            onClick={() => onToolSelect("arrow")}
          >
            <ArrowIcon className="w-4 h-4" />
          </button>
          <button
            className={`tool-button ${selectedTool === "text" ? "active" : ""}`}
            onClick={() => onToolSelect("text")}
          >
            <TextIcon className="w-4 h-4" />
          </button>
          <button
            className={`tool-button ${selectedTool === "blur" ? "active" : ""}`}
            onClick={() => onToolSelect("blur")}
          >
            <BlurIcon className="w-4 h-4" />
          </button>
        </div>
        <div className="tool-group">
          <button className="tool-button" onClick={onUndo} disabled={!canUndo}>
            <UndoIcon className="w-4 h-4" />
          </button>
          <button className="tool-button" onClick={onRedo} disabled={!canRedo}>
            <RedoIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export { Toolbar };
