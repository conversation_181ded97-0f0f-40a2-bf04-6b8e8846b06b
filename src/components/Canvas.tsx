import { useCallback, useEffect, useRef, useState, forwardRef } from "react";
import type { Shape, Tool } from "../types";
import {
  drawArrow,
  drawSelectionBorder,
  getCursorStyle,
  isOverHandle,
  isOverShape,
  resizeShape,
  drawCircle,
  drawLine,
  drawBlur,
} from "../utils/shapes";

interface CanvasProps {
  screenshot: string;
  shapes: Shape[];
  selectedShape: Shape | null;
  currentTool: Tool;
  currentColor: string;
  isEditing: boolean;
  onShapeUpdate: (shapes: Shape[]) => void;
  onSelectedShapeChange: (shape: Shape | null) => void;
  onStartTextEditing: (shape: Shape) => void;
  onFinishTextEditing: () => void;
  onToolChange: (tool: Tool) => void;
  onHistoryUpdate: (shapes: Shape[]) => void;
  onUndo?: () => void;
  onRedo?: () => void;
}

const TEXT_STYLE = {
  fontSize: "16px",
  lineHeight: "1.2",
  fontFamily: "Arial",
  padding: 4,
};

export const Canvas = forwardRef<HTMLCanvasElement, CanvasProps>(({
  screenshot,
  shapes,
  selectedShape,
  currentTool,
  currentColor,
  isEditing,
  onShapeUpdate,
  onSelectedShapeChange,
  onStartTextEditing,
  onFinishTextEditing,
  onToolChange,
  onHistoryUpdate,
  onUndo,
  onRedo,
}, ref) => {
  const localCanvasRef = useRef<HTMLCanvasElement>(null);
  const canvasRef = (ref as React.RefObject<HTMLCanvasElement>) || localCanvasRef;
  const imgRef = useRef<HTMLImageElement>(new Image());
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(
    null
  );
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const textEditOverlayRef = useRef<HTMLDivElement>(null);
  const textBoxRef = useRef<HTMLDivElement>(null);
  const textInputRef = useRef<HTMLTextAreaElement>(null);
  const [scale, setScale] = useState({ x: 1, y: 1 });
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [textInput, setTextInput] = useState({
    isActive: false,
    position: { x: 0, y: 0 },
    value: "",
    shape: null as Shape | null,
  });
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustTextareaHeight = useCallback((textarea: HTMLTextAreaElement) => {
    textarea.style.height = "auto"; // Reset height to recalculate
    textarea.style.height = `${textarea.scrollHeight}px`; // Set to scrollHeight
  }, []);

  const updateShapesWithHistory = useCallback(
    (newShapes: Shape[]) => {
      onShapeUpdate(newShapes);
      if (JSON.stringify(shapes) !== JSON.stringify(newShapes)) {
        onHistoryUpdate(newShapes);
      }
    },
    [shapes, onShapeUpdate, onHistoryUpdate]
  );

  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    // Clear with actual canvas dimensions
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw image at full canvas size
    ctx.drawImage(imgRef.current, 0, 0, canvas.width, canvas.height);

    // Set up the context for proper scaling
    ctx.save();

    shapes.forEach((shape) => {
      // Skip drawing the text shape that's being edited
      if (
        shape.type === "text" &&
        shape === textInput.shape &&
        textInput.isActive
      ) {
        return;
      }

      ctx.beginPath();
      ctx.strokeStyle = shape.color;
      ctx.fillStyle = shape.color;

      // Draw shapes using the original coordinates
      if (shape.type === "rectangle") {
        ctx.strokeRect(shape.x, shape.y, shape.width, shape.height);
      } else if (shape.type === "circle") {
        drawCircle(
          ctx,
          shape.x,
          shape.y,
          shape.width,
          shape.height,
          shape.color
        );
      } else if (shape.type === "line") {
        drawLine(
          ctx,
          shape.startX!,
          shape.startY!,
          shape.endX!,
          shape.endY!,
          shape.color
        );
      } else if (shape.type === "arrow") {
        drawArrow(
          ctx,
          shape.startX!,
          shape.startY!,
          shape.endX!,
          shape.endY!,
          shape.color
        );
      } else if (shape.type === "text") {
        // Set exact same font as textarea
        ctx.font = `${TEXT_STYLE.fontSize} ${TEXT_STYLE.fontFamily}`;
        ctx.fillStyle = shape.color;
        ctx.textBaseline = "top"; // Important for alignment

        // Add clipping to prevent overflow
        ctx.save();
        ctx.beginPath();
        ctx.rect(shape.x, shape.y, shape.width, shape.height);
        ctx.clip();

        const paragraphs = shape.text?.split("\n") || [];
        let y = shape.y + TEXT_STYLE.padding; // Start at top + padding
        const lineHeight =
          parseFloat(TEXT_STYLE.fontSize) * parseFloat(TEXT_STYLE.lineHeight);
        const maxWidth = shape.width - TEXT_STYLE.padding * 2;

        paragraphs.forEach((paragraph) => {
          if (!paragraph) {
            y += lineHeight;
            return;
          }

          let currentLine = "";
          let currentX = shape.x + TEXT_STYLE.padding;

          for (let i = 0; i < paragraph.length; i++) {
            const char = paragraph[i];
            const testLine = currentLine + char;
            const metrics = ctx.measureText(testLine);

            if (metrics.width > maxWidth) {
              ctx.fillText(currentLine, currentX, y);
              currentLine = char;
              y += lineHeight;
            } else {
              currentLine = testLine;
            }
          }

          // Draw remaining text
          if (currentLine) {
            ctx.fillText(currentLine, currentX, y);
            y += lineHeight;
          }
        });

        ctx.restore();
      } else if (shape.type === "blur") {
        drawBlur(ctx, shape.x, shape.y, shape.width, shape.height);
      }

      // Update selection border condition
      if (
        shape === selectedShape &&
        (!textInput.isActive || shape !== textInput.shape)
      ) {
        drawSelectionBorder(ctx, shape, selectedShape);
      }
    });

    ctx.restore();
  }, [shapes, selectedShape, textInput.shape, textInput.isActive]);

  const getScaledMousePosition = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      const rect = canvas?.getBoundingClientRect();
      if (!rect || !canvas) return { x: 0, y: 0 };

      // Convert screen coordinates to canvas coordinates
      const scaleX = canvas.width / rect.width;
      const scaleY = canvas.height / rect.height;

      const x = (e.clientX - rect.left) * scaleX;
      const y = (e.clientY - rect.top) * scaleY;

      return { x, y };
    },
    []
  );

  const canvasToScreenPosition = useCallback(
    (canvasX: number, canvasY: number) => {
      const canvas = canvasRef.current;
      const rect = canvas?.getBoundingClientRect();
      if (!rect || !canvas) return { x: 0, y: 0 };

      const scaleX = rect.width / canvas.width;
      const scaleY = rect.height / canvas.height;

      return {
        x: canvasX * scaleX + rect.left,
        y: canvasY * scaleY + rect.top,
      };
    },
    []
  );

  useEffect(() => {
    const img = imgRef.current;
    img.src = screenshot;
    img.onload = () => {
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        const aspectRatio = img.width / img.height;
        const maxWidth = window.innerWidth * 0.9;
        const maxHeight = window.innerHeight * 0.8;
        let newWidth = img.width;
        let newHeight = img.height;

        if (newWidth > maxWidth) {
          newWidth = maxWidth;
          newHeight = newWidth / aspectRatio;
        }

        if (newHeight > maxHeight) {
          newHeight = maxHeight;
          newWidth = newHeight * aspectRatio;
        }

        // Set canvas dimensions
        canvas.width = newWidth;
        canvas.height = newHeight;
        canvas.style.width = `${newWidth}px`;
        canvas.style.height = `${newHeight}px`;

        redrawCanvas();
      }
    };
  }, [screenshot, redrawCanvas]);

  const updateTextBoxPosition = useCallback((shape: Shape) => {
    if (!textBoxRef.current || !textInputRef.current) return;
    textBoxRef.current.style.display = "block";
    textBoxRef.current.style.left = `${shape.x}px`;
    textBoxRef.current.style.top = `${shape.y}px`;
    textBoxRef.current.style.width = `${shape.width}px`;
    textBoxRef.current.style.height = `${shape.height}px`;
    textInputRef.current.style.width = `${shape.width}px`;
    textInputRef.current.style.height = `${shape.height}px`;
    textInputRef.current.value = shape.text || "";
    textInputRef.current.focus();
  }, []);

  const startTextInput = useCallback(
    (x: number, y: number) => {
      const initialWidth = 100;
      const initialHeight = 24;
      const newShape: Shape = {
        id: shapes.length,
        type: "text",
        x,
        y,
        width: initialWidth,
        height: initialHeight,
        color: currentColor,
        text: "",
      };

      // We should use the canvas coordinates directly since we're using the mirror
      setTextInput({
        isActive: true,
        position: { x, y }, // These are already in canvas coordinates
        value: "",
        shape: newShape,
      });

      onShapeUpdate([...shapes, newShape]);
      onSelectedShapeChange(newShape);

      setTimeout(() => {
        textareaRef.current?.focus();
      }, 0);
    },
    [shapes, currentColor, onShapeUpdate, onSelectedShapeChange]
  );

  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const { x, y } = getScaledMousePosition(e);
      const canvas = canvasRef.current;
      if (!canvas) return;

      if (textInput.isActive) {
        finishTextInput();
        return;
      }

      const clickedShape = shapes.find((shape) => isOverShape(x, y, shape));

      if (clickedShape) {
        onSelectedShapeChange(clickedShape);
        if (clickedShape.type === "text") {
          setTextInput((prev) => ({
            ...prev,
            isActive: false,
            shape: clickedShape,
            value: clickedShape.text || "",
          }));
        }

        const handle = isOverHandle(x, y, clickedShape);
        if (handle) {
          setIsResizing(true);
          setResizeHandle(handle);
        } else {
          setIsDragging(true);
          setStartPoint({ x, y });
        }
      } else {
        onSelectedShapeChange(null);
        if (currentTool === "text") {
          startTextInput(x, y);
        } else if (currentTool !== "cursor") {
          setIsDrawing(true);
          setStartPoint({ x, y });
        }
      }
    },
    [
      shapes,
      currentTool,
      isEditing,
      currentColor,
      onSelectedShapeChange,
      onShapeUpdate,
      onStartTextEditing,
      onFinishTextEditing,
      redrawCanvas,
      updateTextBoxPosition,
      getScaledMousePosition,
      startTextInput,
      canvasToScreenPosition,
    ]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const { x, y } = getScaledMousePosition(e);
      const canvas = canvasRef.current;
      if (!canvas) return;

      if (isResizing && selectedShape) {
        const updatedShapes = shapes.map((shape) => {
          if (shape.id === selectedShape.id) {
            const newShape = { ...selectedShape };
            resizeShape(newShape, x, y, resizeHandle);

            // Update textarea size and position when resizing text shape
            if (newShape.type === "text") {
              // Ensure minimum dimensions
              newShape.width = Math.max(newShape.width, 50);
              newShape.height = Math.max(newShape.height, 24);

              // Update textarea if it exists
              if (textareaRef.current) {
                textareaRef.current.style.width = `${newShape.width}px`;
                textareaRef.current.style.height = `${newShape.height}px`;
              }

              // Update textInput state with new dimensions
              setTextInput((prev) => ({
                ...prev,
                shape: newShape,
                position: canvasToScreenPosition(newShape.x, newShape.y),
              }));
            }
            return newShape;
          }
          return shape;
        });
        onShapeUpdate(updatedShapes);
        redrawCanvas();
      } else if (isDragging && selectedShape && startPoint) {
        const dx = x - startPoint.x;
        const dy = y - startPoint.y;

        const updatedShapes = shapes.map((shape) => {
          if (shape.id === selectedShape.id) {
            const newShape = { ...shape };
            if (newShape.type === "arrow" || newShape.type === "line") {
              newShape.startX! += dx;
              newShape.startY! += dy;
              newShape.endX! += dx;
              newShape.endY! += dy;
            } else {
              newShape.x += dx;
              newShape.y += dy;
            }

            // Update textarea position when dragging text shape
            if (newShape.type === "text") {
              setTextInput((prev) => ({
                ...prev,
                shape: newShape,
              }));
            }
            return newShape;
          }
          return shape;
        });

        setStartPoint({ x, y });
        onShapeUpdate(updatedShapes);
        redrawCanvas();
      } else if (isDrawing && startPoint) {
        // Preview the shape being drawn
        redrawCanvas();
        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        ctx.beginPath();
        ctx.strokeStyle = currentColor;
        ctx.fillStyle = currentColor;

        if (currentTool === "rectangle") {
          ctx.strokeRect(
            startPoint.x,
            startPoint.y,
            x - startPoint.x,
            y - startPoint.y
          );
        } else if (currentTool === "circle") {
          drawCircle(
            ctx,
            startPoint.x,
            startPoint.y,
            x - startPoint.x,
            y - startPoint.y,
            currentColor
          );
        } else if (currentTool === "line") {
          drawLine(ctx, startPoint.x, startPoint.y, x, y, currentColor);
        } else if (currentTool === "arrow") {
          drawArrow(ctx, startPoint.x, startPoint.y, x, y, currentColor);
        } else if (currentTool === "blur") {
          drawBlur(
            ctx,
            startPoint.x,
            startPoint.y,
            x - startPoint.x,
            y - startPoint.y
          );
        }
      } else {
        const shape = shapes.find((s) => isOverShape(x, y, s));
        const handle = shape ? isOverHandle(x, y, shape) : null;

        canvas.style.cursor = shape
          ? handle
            ? getCursorStyle(handle)
            : "move"
          : currentTool === "cursor"
            ? "default"
            : "crosshair";
      }
    },
    [
      isResizing,
      isDragging,
      isDrawing,
      selectedShape,
      startPoint,
      shapes,
      currentTool,
      currentColor,
      onShapeUpdate,
      redrawCanvas,
      getScaledMousePosition,
      canvasToScreenPosition,
    ]
  );

  const handleMouseUp = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const { x, y } = getScaledMousePosition(e);
      if (isResizing || isDragging) {
        setIsResizing(false);
        setIsDragging(false);
        setResizeHandle("");
        updateShapesWithHistory([...shapes]);
      } else if (isDrawing && startPoint) {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const newShape: Shape = {
          id: shapes.length,
          type: currentTool,
          x: Math.min(startPoint.x, x),
          y: Math.min(startPoint.y, y),
          width: Math.abs(x - startPoint.x),
          height: Math.abs(y - startPoint.y),
          color: currentColor,
        };

        if (currentTool === "line" || currentTool === "arrow") {
          newShape.startX = startPoint.x;
          newShape.startY = startPoint.y;
          newShape.endX = x;
          newShape.endY = y;
        }

        updateShapesWithHistory([...shapes, newShape]);
        onSelectedShapeChange(newShape);

        if (currentTool !== "text") {
          onToolChange("cursor");
        }
      }

      setIsDrawing(false);
      setStartPoint(null);
      redrawCanvas();
    },
    [
      isResizing,
      isDragging,
      isDrawing,
      currentTool,
      shapes,
      startPoint,
      currentColor,
      onShapeUpdate,
      onSelectedShapeChange,
      onToolChange,
      redrawCanvas,
      getScaledMousePosition,
      updateShapesWithHistory,
    ]
  );

  const handleTextInput = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (!textInput.shape || !textareaRef.current) return;

      const newText = e.target.value;
      adjustTextareaHeight(e.target);

      const newWidth = textareaRef.current.offsetWidth;
      const newHeight = textareaRef.current.scrollHeight;

      const updatedShape = {
        ...textInput.shape,
        text: newText,
        width: newWidth,
        height: newHeight,
      };

      setTextInput((prev) => ({
        ...prev,
        value: newText,
        shape: updatedShape,
      }));

      const updatedShapes = shapes.map((s) =>
        s.id === textInput.shape.id ? updatedShape : s
      );
      onShapeUpdate(updatedShapes);
      redrawCanvas();
    },
    [shapes, onShapeUpdate, redrawCanvas, textInput.shape, adjustTextareaHeight]
  );

  const finishTextInput = useCallback(() => {
    if (textInput.shape) {
      if (
        !textInput.shape.text &&
        !shapes.find((s) => s.id === textInput.shape!.id)
      ) {
        const newShapes = shapes.filter((s) => s.id !== textInput.shape!.id);
        onShapeUpdate(newShapes);
      } else {
        const updatedShapes = shapes.map((s) =>
          s.id === textInput.shape.id ? textInput.shape : s
        );
        onShapeUpdate(updatedShapes);
      }
    }

    setTextInput({
      isActive: false,
      position: { x: 0, y: 0 },
      value: "",
      shape: null,
    });

    // Always switch back to cursor after finishing text input
    onToolChange("cursor");
  }, [shapes, textInput.shape, onShapeUpdate, onToolChange]);

  const handleDoubleClick = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const displayToInternalScaleX = canvas.width / rect.width;
      const displayToInternalScaleY = canvas.height / rect.height;

      // Convert screen coordinates to canvas coordinates
      const x = (e.clientX - rect.left) * displayToInternalScaleX;
      const y = (e.clientY - rect.top) * displayToInternalScaleY;

      const clickedShape = shapes.find(
        (shape) => shape.type === "text" && isOverShape(x, y, shape)
      );

      if (clickedShape?.type === "text") {
        setTextInput({
          isActive: true,
          position: { x: clickedShape.x, y: clickedShape.y },
          value: clickedShape.text || "",
          shape: clickedShape,
        });
        setTimeout(() => {
          textareaRef.current?.focus();
        }, 0);
      }
    },
    [shapes, getScaledMousePosition]
  );

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onSelectedShapeChange(null);
        redrawCanvas();
      }

      // Handle undo/redo
      if ((e.metaKey || e.ctrlKey) && e.key === "z") {
        e.preventDefault();
        if (e.shiftKey) {
          onRedo?.();
        } else {
          onUndo?.();
        }
      }

      // Handle delete
      if ((e.key === "Delete" || e.key === "Backspace") && selectedShape) {
        e.preventDefault();
        const newShapes = shapes.filter((s) => s !== selectedShape);
        updateShapesWithHistory(newShapes);
        onSelectedShapeChange(null);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [
    selectedShape,
    shapes,
    onSelectedShapeChange,
    redrawCanvas,
    updateShapesWithHistory,
  ]);

  // Update getMirrorPosition to correctly calculate textarea position
  const getMirrorPosition = useCallback((x: number, y: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    // We need to scale from canvas coordinates to screen coordinates
    const scaleX = rect.width / canvas.width;
    const scaleY = rect.height / canvas.height;

    return {
      x: x * scaleX,
      y: y * scaleY,
    };
  }, []);

  // Update mirror container style
  const mirrorContainerStyle: React.CSSProperties = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    pointerEvents: "none",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  };

  // Update the useEffect that handles mirror sizing
  useEffect(() => {
    const canvas = canvasRef.current;
    const img = imgRef.current;

    const updateCanvasAndMirror = () => {
      const { width: boundWidth, height: boundHeight } =
        canvas.getBoundingClientRect();
      const mirror = canvas.nextElementSibling as HTMLDivElement;

      if (mirror) {
        mirror.style.width = `${boundWidth}px`;
        mirror.style.height = `${boundHeight}px`;
        setScale({
          x: canvas.width / boundWidth,
          y: canvas.height / boundHeight,
        });
      }
    };

    const resizeObserver = new ResizeObserver(updateCanvasAndMirror);
    resizeObserver.observe(canvas);

    // Initial setup
    img.onload = () => {
      const aspectRatio = img.width / img.height;
      const maxWidth = window.innerWidth * 0.9;
      const maxHeight = window.innerHeight * 0.8;
      let newWidth = img.width;
      let newHeight = img.height;

      if (newWidth > maxWidth) {
        newWidth = maxWidth;
        newHeight = newWidth / aspectRatio;
      }
      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = newHeight * aspectRatio;
      }

      canvas.width = newWidth;
      canvas.height = newHeight;
      updateCanvasAndMirror();
      redrawCanvas();
    };

    return () => resizeObserver.disconnect();
  }, [redrawCanvas]);

  // Add back the ResizeObserver effect
  useEffect(() => {
    if (!textareaRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      const target = entries[0].target as HTMLTextAreaElement;
      if (textInput.shape && textInput.isActive) {
        // Only sync when actively editing
        const newShape = {
          ...textInput.shape,
          width: target.offsetWidth,
          height: target.offsetHeight,
        };
        setTextInput((prev) => ({
          ...prev,
          shape: newShape,
        }));
        const updatedShapes = shapes.map((s) =>
          s.id === textInput.shape.id ? newShape : s
        );
        onShapeUpdate(updatedShapes);
        redrawCanvas();
      }
    });

    resizeObserver.observe(textareaRef.current);
    return () => resizeObserver.disconnect();
  }, [
    textInput.shape,
    textInput.isActive,
    shapes,
    onShapeUpdate,
    redrawCanvas,
  ]);

  return (
    <div
      className="canvas-wrapper"
      style={{
        position: "relative",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "100%",
        overflow: "hidden",
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          maxWidth: "100%",
          maxHeight: "100%",
          width: "auto",
          height: "auto",
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onDoubleClick={handleDoubleClick}
      />
      <div style={mirrorContainerStyle}>
        <div
          style={{
            position: "absolute",
            left: `${getMirrorPosition(textInput.shape?.x || 0, textInput.shape?.y || 0).x}px`,
            top: `${getMirrorPosition(textInput.shape?.x || 0, textInput.shape?.y || 0).y}px`,
            width: `${textInput.shape?.width || 100}px`,
            height: `${textInput.shape?.height || 24}px`,
            pointerEvents: textInput.isActive ? "auto" : "none",
            display: textInput.shape ? "block" : "none",
            opacity: textInput.isActive ? 1 : 0,
            transform: `scale(${1 / scale.x}, ${1 / scale.y})`,
            transformOrigin: "top left",
          }}
        >
          <textarea
            ref={textareaRef}
            style={{
              position: "absolute",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
              padding: `${TEXT_STYLE.padding}px`,
              color: currentColor,
              border: "none",
              background: "transparent",
              resize: "both",
              overflow: "auto",
              outline: "none",
              fontFamily: TEXT_STYLE.fontFamily,
              fontSize: TEXT_STYLE.fontSize,
              lineHeight: TEXT_STYLE.lineHeight,
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              boxSizing: "border-box",
              minWidth: "50px",
              minHeight: "24px",
            }}
            value={textInput.value}
            onChange={handleTextInput}
            onBlur={finishTextInput}
            onKeyDown={(e) => {
              if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                finishTextInput();
              }
            }}
            autoFocus={textInput.isActive}
          />
        </div>
      </div>
    </div>
  );
});
