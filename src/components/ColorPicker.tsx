import React, { useState, useRef, useEffect } from "react";

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

const colors = [
  { name: "Red", value: "#ff0000" },
  { name: "<PERSON>", value: "#00ff00" },
  { name: "<PERSON>", value: "#0000ff" },
  { name: "Yellow", value: "#ffff00" },
  { name: "<PERSON><PERSON><PERSON>", value: "#ff00ff" },
  { name: "<PERSON><PERSON>", value: "#00ffff" },
  { name: "Black", value: "#000000" },
  { name: "White", value: "#ffffff" },
];

export function ColorPicker({ color, onChange }: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleColorSelect = (newColor: string) => {
    onChange(newColor);
    setIsOpen(false);
  };

  return (
    <div className="color-picker" ref={dropdownRef}>
      <button
        className="color-circle"
        onClick={toggleDropdown}
        style={{ backgroundColor: color }}
      />
      {isOpen && (
        <div className="color-picker-dropdown">
          <div className="color-options">
            {colors.map((c) => (
              <button
                key={c.value}
                className="color-option"
                onClick={() => handleColorSelect(c.value)}
              >
                <div
                  className="color-swatch"
                  style={{ backgroundColor: c.value }}
                />
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
