import React, { useCallback, useEffect, useState } from "react";
import "../styles/capture-overlay.css";

interface CaptureOverlayProps {
  onCapture: (bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  }) => void;
  onCancel: () => void;
}

export const CaptureOverlay: React.FC<CaptureOverlayProps> = ({
  onCapture,
  onCancel,
}) => {
  const [isSelecting, setIsSelecting] = useState(false);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [selectionBox, setSelectionBox] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  const getMousePosition = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }, []);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      const { x, y } = getMousePosition(e);

      setIsSelecting(true);
      setStartPoint({ x, y });
      setSelectionBox({ x, y, width: 0, height: 0 });
    },
    [getMousePosition]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isSelecting) return;

      const { x: currentX, y: currentY } = getMousePosition(e);

      setSelectionBox({
        x: Math.min(startPoint.x, currentX),
        y: Math.min(startPoint.y, currentY),
        width: Math.abs(currentX - startPoint.x),
        height: Math.abs(currentY - startPoint.y),
      });
    },
    [isSelecting, startPoint, getMousePosition]
  );

  const handleMouseUp = useCallback(() => {
    if (isSelecting) {
      setIsSelecting(false);
      if (selectionBox.width > 10 && selectionBox.height > 10) {
        const dpr = window.devicePixelRatio || 1;

        onCapture({
          x: Math.round(selectionBox.x * dpr),
          y: Math.round(selectionBox.y * dpr),
          width: Math.round(selectionBox.width * dpr),
          height: Math.round(selectionBox.height * dpr),
        });
      }
    }
  }, [isSelecting, selectionBox, onCapture]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onCancel();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [onCancel]);

  return (
    <div
      className="capture-overlay"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      {isSelecting && (
        <>
          {/* Top overlay */}
          <div
            className="overlay-background"
            style={{
              top: 0,
              left: 0,
              width: "100%",
              height: selectionBox.y,
            }}
          />
          {/* Bottom overlay */}
          <div
            className="overlay-background"
            style={{
              top: selectionBox.y + selectionBox.height,
              left: 0,
              width: "100%",
              height: `calc(100% - ${selectionBox.y + selectionBox.height}px)`,
            }}
          />
          {/* Left overlay */}
          <div
            className="overlay-background"
            style={{
              top: selectionBox.y,
              left: 0,
              width: selectionBox.x,
              height: selectionBox.height,
            }}
          />
          {/* Right overlay */}
          <div
            className="overlay-background"
            style={{
              top: selectionBox.y,
              left: selectionBox.x + selectionBox.width,
              width: `calc(100% - ${selectionBox.x + selectionBox.width}px)`,
              height: selectionBox.height,
            }}
          />
        </>
      )}

      <div
        className="selection-box"
        style={{
          display: isSelecting ? "block" : "none",
          left: selectionBox.x,
          top: selectionBox.y,
          width: selectionBox.width,
          height: selectionBox.height,
        }}
      />

      {!isSelecting && (
        <div className="overlay-instructions">
          Click and drag to select an area to capture
        </div>
      )}
    </div>
  );
};
