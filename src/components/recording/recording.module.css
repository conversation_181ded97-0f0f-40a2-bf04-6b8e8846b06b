/* Recording overlay styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding-bottom: 20px;
}

/* Controls container */
.controlsContainer {
  position: relative;
  background-color: rgba(30, 30, 30, 0.8);
  border-radius: 50px;
  padding: 10px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  cursor: move;
  user-select: none;
  max-width: 500px;
  margin: 0 auto;
}

.controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

/* Recording status and timer */
.recordingStatus {
  display: flex;
  align-items: center;
  gap: 10px;
}

.recordingIndicator {
  color: red;
  animation: pulse 1.5s infinite;
}

.timer {
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  color: white;
  min-width: 60px;
}

/* Audio controls */
.audioControls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.activeButton {
  color: white;
  background-color: rgba(0, 128, 128, 0.6);
}

.activeButton:hover {
  background-color: rgba(0, 128, 128, 0.8);
}

.inactiveButton {
  color: #888;
  background-color: rgba(80, 80, 80, 0.6);
}

.inactiveButton:hover {
  background-color: rgba(80, 80, 80, 0.8);
}

/* Control buttons */
.controlButtons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.controlButton {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.controlButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.startButton {
  background-color: rgba(0, 128, 0, 0.6);
}

.startButton:hover {
  background-color: rgba(0, 128, 0, 0.8);
}

.pauseButton {
  background-color: rgba(255, 165, 0, 0.6);
}

.pauseButton:hover {
  background-color: rgba(255, 165, 0, 0.8);
}

.resumeButton {
  background-color: rgba(0, 128, 0, 0.6);
}

.resumeButton:hover {
  background-color: rgba(0, 128, 0, 0.8);
}

.stopButton {
  background-color: rgba(0, 0, 255, 0.6);
}

.stopButton:hover {
  background-color: rgba(0, 0, 255, 0.8);
}

.resetButton {
  background-color: rgba(128, 128, 128, 0.6);
}

.resetButton:hover {
  background-color: rgba(128, 128, 128, 0.8);
}

.deleteButton {
  background-color: rgba(255, 0, 0, 0.6);
}

.deleteButton:hover {
  background-color: rgba(255, 0, 0, 0.8);
}

/* Animation for recording indicator */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Draggable styles */
.draggable {
  cursor: move;
}
