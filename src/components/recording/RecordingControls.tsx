import React from "react";
import { useRecordingState } from "~hooks/useRecordingState";
import RecordingTimer from "./RecordingTimer";
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ResetIcon,
  RecordIcon,
} from "./RecordingIcons";

/**
 * RecordingControls component
 *
 * Provides controls for recording, including:
 * - Start/pause/resume recording
 * - Stop recording (save)
 * - Reset recording
 * - Delete recording (discard)
 *
 * Note: Microphone and system audio are always enabled
 */
const RecordingControls: React.FC = () => {
  const {
    recordingState,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    resetRecording,
    openRecordingPage,
  } = useRecordingState();

  const handleReset = () => {
    // Use the reset recording function
    resetRecording();
  };

  return (
    <div className="relative bg-[#1A1D24]/90 rounded-full py-2 px-5 shadow-lg backdrop-blur-sm cursor-grab select-none max-w-xl mx-auto border border-[#252830]">
      <div className="flex items-center justify-between gap-4">
        {/* Recording indicator and timer */}
        <div className="flex items-center gap-2">
          {recordingState.isRecording && !recordingState.isPaused && (
            <div className="text-[#00E5C7] animate-pulse">
              <RecordIcon />
            </div>
          )}
          <RecordingTimer />
        </div>

        {/* Audio is always enabled */}

        {/* Control buttons */}
        <div className="flex gap-3 items-center">
          {!recordingState.isRecording ? (
            <>
              {/* Start button */}
              <button
                className="bg-[#1A1D24] cursor-pointer px-3 py-1 flex items-center justify-center transition-all duration-200 hover:bg-[#252830] text-white"
                onClick={startRecording}
                title="Start Recording"
              >
                <PlayIcon />
              </button>

              {/* Open recording page button */}
              <button
                className="bg-[#1A1D24] cursor-pointer px-3 py-1 flex items-center justify-center transition-all duration-200 hover:bg-[#252830] text-white text-xs"
                onClick={openRecordingPage}
                title="Open Recording Page"
              >
                Open Recording Page
              </button>
            </>
          ) : (
            // Recording in progress controls
            <>
              {recordingState.isPaused ? (
                // Resume button
                <button
                  className="bg-[#1A1D24] cursor-pointer px-3 py-1 flex items-center justify-center transition-all duration-200 hover:bg-[#252830] text-white"
                  onClick={resumeRecording}
                  title="Resume Recording"
                >
                  <PlayIcon />
                </button>
              ) : (
                // Pause button
                <button
                  className="bg-[#1A1D24] cursor-pointer px-3 py-1 flex items-center justify-center transition-all duration-200 hover:bg-[#252830] text-white"
                  onClick={pauseRecording}
                  title="Pause Recording"
                >
                  <PauseIcon />
                </button>
              )}

              {/* Stop button */}
              <button
                className="bg-[#1A1D24] cursor-pointer px-3 py-1 flex items-center justify-center transition-all duration-200 hover:bg-[#252830] text-white"
                onClick={stopRecording}
                title="Stop and Save Recording"
              >
                <StopIcon />
              </button>

              {/* Reset button */}
              <button
                className="bg-[#1A1D24] cursor-pointer px-3 py-1 flex items-center justify-center transition-all duration-200 hover:bg-[#252830] text-white"
                onClick={handleReset}
                title="Reset Recording"
              >
                <ResetIcon />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecordingControls;
