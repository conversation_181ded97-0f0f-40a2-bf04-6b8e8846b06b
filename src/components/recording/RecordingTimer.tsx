import React, { useEffect, useState, useRef } from "react";
import { useRecordingState } from "~hooks/useRecordingState";

/**
 * RecordingTimer component
 *
 * Displays the current recording time in MM:SS format
 * Uses a simple local timer based on performance.now() for accurate display
 */
const RecordingTimer: React.FC = () => {
  const { recordingState } = useRecordingState();
  const [displayTime, setDisplayTime] = useState("00:00");

  // Set up timer when recording starts/stops/pauses
  useEffect(() => {
    // Clear any existing timer
    setDisplayTime(recordingState.recordingTime);
  }, [
    recordingState.isRecording,
    recordingState.isPaused,
    recordingState.recordingTime,
  ]);

  return (
    <div className="font-mono text-lg font-bold text-[#FFFFFF] min-w-[120px]">
      {displayTime}
    </div>
  );
};

export default RecordingTimer;
