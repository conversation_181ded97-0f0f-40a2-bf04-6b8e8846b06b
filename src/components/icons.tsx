import React from "react";
import logo from "data-base64:~assets/icon.png";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
}

export const CursorIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z" />
    <path d="m13 13 6 6" />
  </svg>
);

export const RectangleIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
  </svg>
);

export const CircleIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10" />
  </svg>
);

export const LineIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg width="21" height="21" viewBox="0 0 21 21" fill="none" {...props}>
    <line
      x1="1.07024"
      y1="19.9789"
      x2="19.5336"
      y2="1.51559"
      stroke="white"
      strokeWidth="2"
    />
  </svg>
);

export const ArrowIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg width="21" height="21" viewBox="0 0 21 21" fill="none" {...props}>
    <path
      d="M20.7407 1.48197C20.7407 0.929683 20.293 0.481968 19.7407 0.481968L10.7407 0.481969C10.1884 0.481968 9.74069 0.929684 9.74069 1.48197C9.74069 2.03425 10.1884 2.48197 10.7407 2.48197L18.7407 2.48197L18.7407 10.482C18.7407 11.0343 19.1884 11.482 19.7407 11.482C20.293 11.482 20.7407 11.0343 20.7407 10.482L20.7407 1.48197ZM1.98445 20.6524L20.4478 2.18907L19.0336 0.774862L0.570237 19.2382L1.98445 20.6524Z"
      fill="white"
    />
  </svg>
);

export const TextIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" {...props}>
    <path
      d="M18.2961 4.09424H4.37013C4.13929 4.09424 3.91791 4.18597 3.75468 4.34925C3.59146 4.51253 3.49976 4.73399 3.49976 4.9649V7.57689C3.49976 7.80781 3.59146 8.02926 3.75468 8.19254C3.91791 8.35582 4.13929 8.44755 4.37013 8.44755C4.60096 8.44755 4.82234 8.35582 4.98557 8.19254C5.1488 8.02926 5.2405 7.80781 5.2405 7.57689V5.83556H10.4627V17.1542H8.72198C8.49114 17.1542 8.26976 17.2459 8.10653 17.4092C7.94331 17.5725 7.85161 17.7939 7.85161 18.0248C7.85161 18.2558 7.94331 18.4772 8.10653 18.6405C8.26976 18.8038 8.49114 18.8955 8.72198 18.8955H13.9442C14.175 18.8955 14.3964 18.8038 14.5596 18.6405C14.7229 18.4772 14.8146 18.2558 14.8146 18.0248C14.8146 17.7939 14.7229 17.5725 14.5596 17.4092C14.3964 17.2459 14.175 17.1542 13.9442 17.1542H12.2035V5.83556H17.4257V7.57689C17.4257 7.80781 17.5174 8.02926 17.6806 8.19254C17.8438 8.35582 18.0652 8.44755 18.2961 8.44755C18.5269 8.44755 18.7483 8.35582 18.9115 8.19254C19.0747 8.02926 19.1664 7.80781 19.1664 7.57689V4.9649C19.1664 4.73399 19.0747 4.51253 18.9115 4.34925C18.7483 4.18597 18.5269 4.09424 18.2961 4.09424Z"
      fill="white"
    />
  </svg>
);

export const CloseIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <line x1="18" y1="6" x2="6" y2="18" />
    <line x1="6" y1="6" x2="18" y2="18" />
  </svg>
);

export const UndoIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M3 7v6h6" />
    <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13" />
  </svg>
);

export const RedoIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M21 7v6h-6" />
    <path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7" />
  </svg>
);

export const SettingsIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="3" />
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
  </svg>
);
export const LogoIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <img src={logo} alt="Logo" width={size} height={size} />
);

export const BlurIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg width="27" height="27" viewBox="0 0 27 27" fill="none" {...props}>
    <rect width="26.1111" height="26.1111" fill="url(#pattern0_5907_24069)" />
    <defs>
      <pattern
        id="pattern0_5907_24069"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use xlinkHref="#image0_5907_24069" transform="scale(0.01)" />
      </pattern>
      <image
        id="image0_5907_24069"
        width="100"
        height="100"
        xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAADwElEQVR4nO2dzU4UQRSFm8DGv5cANwqKKzT4AINBEhHeQJ5BXCi4NEYXJAO6NCC+A2DAeQl/NupGMRoIuiBREj5TmTLptDVjd0/1TNl9vn3dvnXPVFXXnaRPFAkhhBA9BhgArgB3gFqO8ZkIIG4NmLdzHohCARgBngI/YvOqV0CQemzYd+AJMBz1CuAS8LLFvBoVEKThGH4MbAGjUbcATgL3gV9t5rWfI24mAoi71ybMEbAEnImKBDgLvGuTyCdgDbgF9EclBei3czRz/dymHm+BoaKSuAp8a/HgbWC6zCL8Q5ybwE6L2nwFxqMCxDh0POw9cM3rw/5jgEngg6NOh95EsduUa2U8M+eJl4eUCJpn7GqLldLZ9gWcdpwZ5k3inrcZlBCgD1iwtYrzBjjVSeDHDqXves2+xACLjvo97OSeceTYpvq8Z17ulbKaqOFRrnuK49JnDnCdGRkxW5TjoN/MGmTYsf9dz5qMaGLeRB1b14UoLbY3FWc79WDhBHiVqOlKlq5tvFFouJFqsGgJMJOo6UGqLrFtJyfbIZW7gfvG/tB3E7UdSzPQ/J8RZ9V7dhUFWE/Udj7NoAlg2baX900TrSvZVgBgzta0YWs8kSeItitPqJZCCCGEEEIIIbKTbNrnCCF8IkECQ4IEVl8JUiwSJDAkSGBIkMCQIIEhQQJDggSGBAkMCRIYuucFhgQJDAkihBBCCCGECB5dXAJDggSGBCkWdXsDQ4IEhgQJDAkSGBIkMCRIYEiQwJAggSFBAkMX78CQIIEhQYQQQgghhBBCdIY+bdrjWjo+pDznMadKQ54PKTs+Nb7WlWwrAPAiUdvbaQZdTgwyPn36urWfj/F/yfMx/gHr7RpnutOEqg4wm8uuwg42RrtxdgrPuOTwt3fucpbB5x2WR1OFZlw9y6ORrEGM63EcY2wlU7B8pmAfE7XcyBNo1GGbZyzgZJuXzTbveaKGxmX7Yt6AjxxLbSFXsApC0+Y8yYNOl5uxoI5zbG1FtVLar4xFxzn8uuNt3xjqWmNdHNuXzhT3jzi5TWFrOBj5wFhPt7DvNgf9pJeHlABgynGA+7XvTojiWilY08SZ1BedEkHzIj3ruGfEV8Z4UQ8fshbUrdi1y3WuzO0WoN/Ocd3RDkmeGYNFJ3PCvj38bJPIfoZ4mYh6HDcWf69NOHNdWDJ+9FG3sPeUTcebhKHR68JRUNxYfNcWZWqxkfue4QPbZlmxzbI/1CsgSD02/MD+v3EuCuxwM637eaBWAUFqdq5jVXyZEUIIEQXHb9rJV72OkB3GAAAAAElFTkSuQmCC"
      />
    </defs>
  </svg>
);

export const TimeMachineIcon: React.FC<IconProps> = ({
  size = 24,
  ...props
}) => (
  <svg width="26" height="26" viewBox="0 0 26 26" fill="none" {...props}>
    <rect width="26" height="26" fill="url(#pattern0_5907_24107)" />
    <defs>
      <pattern
        id="pattern0_5907_24107"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use xlinkHref="#image0_5907_24107" transform="scale(0.01)" />
      </pattern>
      <image
        id="image0_5907_24107"
        width="100"
        height="100"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
);

export const PlusIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10" />
    <path d="M8 12h8" />
    <path d="M12 8v8" />
  </svg>
);

export const RecordingIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <circle cx="8" cy="8" r="8" fill="white" />
  </svg>
);

export const CaptureIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="white">
    <path d="M8 3L15 13H1L8 3Z" />
  </svg>
);
