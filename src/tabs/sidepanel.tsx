import styled from "styled-components";
import { useGuideCapture } from "~hooks/sidepannel/use-guide-capture";
import { useEffect } from "react";

import "../styles/sidepanel.css";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #ffffff;
  height: 100vh;
  position: relative;
  width: 100%;
`;

const Content = styled.div`
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; // Space for footer
  padding-inline: 10px;
`;

const Header = styled.div`
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 30px;
  padding: 20px 20px 0;
  position: relative;
`;

const StepsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const StepItem = styled.div`
  display: flex;
  gap: 16px;
  align-items: flex-start;
  padding: 10px;
  border-radius: 8px;
  background-color: #2a2a2a;
  position: relative;
  padding-right: 20px;

  &:hover {
    background-color: #333333;

    .delete-button {
      opacity: 1;
    }
  }
`;

const StepNumber = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #999;
`;

const StepContent = styled.div`
  flex: 1;
  font-size: 16px;
  color: #fff;
  word-break: break-all;
  display: flex;
  flex-direction: column;
  padding-right: 20px;
`;

const StepImage = styled.img`
  max-width: 200px;
  border-radius: 4px;
  margin-top: 10px;
`;

const NoGuide = styled.div`
  text-align: center;
  color: #999;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
`;

const WelcomeTitle = styled.h1`
  font-size: 32px;
  color: #fff;
  margin: 0;
  font-weight: bold;
`;

const WelcomeText = styled.p`
  font-size: 18px;
  color: #fff;
  margin: 0;
`;

const StartButton = styled.button`
  background-color: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const CircleButton = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #d0330f;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0 0 10px rgba(255, 107, 74, 0.1),
    0 0 0 20px rgba(255, 107, 74, 0.05);
`;

const ButtonText = styled.span`
  color: #fff;
  font-size: 18px;
  font-weight: 500;
`;

const Footer = styled.div`
  background-color: #2a2a2a;
  padding: 16px;
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #333;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  padding-inline: 20px;
  position: relative;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    color: #ff4444;
    background-color: rgba(255, 68, 68, 0.1);
    transform: translateY(-1px);

    &::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      padding: 4px 8px;
      background-color: #333;
      color: white;
      font-size: 12px;
      border-radius: 4px;
      white-space: nowrap;
      margin-bottom: 4px;
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const SaveIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
    <polyline points="17 21 17 13 7 13 7 21" />
    <polyline points="7 3 7 8 15 8" />
  </svg>
);

const ButtonContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #fff;
`;

const ToolbarButton = styled.button<{ variant?: "primary" | "secondary" }>`
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  background-color: ${(props) =>
    props.variant === "primary" ? "#d0330f" : "#d0330f"};
  color: #fff;
  min-width: 120px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.variant === "primary" ? "#ff6b4a" : "#ff6b4a"};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const DeleteButton = styled.button`
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  &:hover {
    color: #ff4444;
    background-color: rgba(255, 68, 68, 0.1);
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const TrashIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
  </svg>
);

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
  margin-right: 8px;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const Error = styled.div`
  color: #ff4444;
  font-size: 14px;
  margin-top: 10px;
`;

const TitleInput = styled.input`
  width: 100%;
  padding: 8px;
  margin-bottom: 20px;
  border: 1px solid #333;
  border-radius: 4px;
  background-color: #2a2a2a;
  color: #fff;
`;

const StepDescription = styled.textarea`
  background: transparent;
  border: 1px solid transparent;
  color: #fff;
  font-size: 14px;
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  min-height: 24px;
  resize: vertical;
  font-family: inherit;

  &:hover {
    border-color: #444;
    background: #333;
  }

  &:focus {
    border-color: #666;
    background: #333;
    outline: none;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: #ff4444;
    background-color: rgba(255, 68, 68, 0.1);
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const CloseIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

function IndexSidePanel() {
  const {
    currentGuide,
    isRecording,
    isExporting,
    handleExport,
    handleStopRecording,
    handleStartRecording,
    deleteStep,
    deleteCurrentGuide,
    error,
    title,
    setTitle,
    updateStepDescription,
  } = useGuideCapture();

  const showWelcome =
    !isRecording && (!currentGuide || currentGuide.steps.length === 0);

  const handleClose = async () => {
    if (isRecording) {
      handleStopRecording();
    }
    await deleteCurrentGuide();
    chrome.sidePanel.setOptions({
      enabled: false,
      path: "tabs/sidepanel.html",
    });
  };

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        if (isRecording) {
          handleStopRecording();
        }
        deleteCurrentGuide();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isRecording, handleStopRecording, deleteCurrentGuide]);

  return (
    <Container>
      <Header>
        Talknician
        <CloseButton onClick={handleClose} title="Close extension">
          <CloseIcon />
        </CloseButton>
      </Header>
      <Content>
        {currentGuide && (
          <>
            <TitleInput
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Untitled Guide"
            />
            <StepsList>
              {currentGuide.steps.map((step, index) => (
                <StepItem key={index}>
                  <StepNumber>{index + 1}</StepNumber>
                  <StepContent>
                    <StepDescription
                      value={step.description || ""}
                      onChange={(e) =>
                        updateStepDescription(index, e.target.value)
                      }
                      placeholder="Click to add description..."
                      rows={1}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          e.currentTarget.blur();
                        }
                      }}
                    />
                    {step.screenshot && (
                      <StepImage
                        src={step.screenshot}
                        alt={`Step ${index + 1}`}
                      />
                    )}
                  </StepContent>
                  <DeleteButton
                    className="delete-button"
                    onClick={() => deleteStep(index)}
                    title="Delete step"
                  >
                    <TrashIcon />
                  </DeleteButton>
                </StepItem>
              ))}
              {isRecording && currentGuide.steps.length === 0 && (
                <NoGuide>
                  Start interacting with the page to record steps
                </NoGuide>
              )}
            </StepsList>
          </>
        )}
        {showWelcome && (
          <NoGuide>
            <WelcomeTitle>Hi 👋</WelcomeTitle>
            <WelcomeText>Ready to capture a new guide?</WelcomeText>
            <StartButton onClick={handleStartRecording}>
              <CircleButton />
              <ButtonText>Start capture</ButtonText>
            </StartButton>
          </NoGuide>
        )}
      </Content>

      {currentGuide && (
        <Footer>
          {isRecording ? (
            <ToolbarButton variant="primary" onClick={handleStopRecording}>
              Stop Recording
            </ToolbarButton>
          ) : (
            <>
              <IconButton
                onClick={handleExport}
                disabled={!currentGuide.steps.length || isExporting}
                data-tooltip="Save & Go to Guide"
              >
                <ButtonContent>
                  {isExporting && <LoadingSpinner />}
                  <SaveIcon />
                </ButtonContent>
              </IconButton>
              <IconButton
                onClick={deleteCurrentGuide}
                data-tooltip="Discard"
                disabled={!currentGuide.steps.length || isExporting}
              >
                <TrashIcon />
              </IconButton>
            </>
          )}
          {error && <Error>{error}</Error>}
        </Footer>
      )}
    </Container>
  );
}

export default IndexSidePanel;
