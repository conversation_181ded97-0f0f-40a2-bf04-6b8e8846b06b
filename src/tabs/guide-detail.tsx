import React from "react";
import styles from "data-text:../styles/guide-detail.css";
import { getWebsiteUrl } from "~utils/domain-config";
import type { GuideResponse } from "~/background/api";

interface GuideDetailProps {
  guideId: string;
  onBack: () => void;
  allGuides: GuideResponse[];
}

export default function GuideDetail({
  guideId,
  onBack,
  allGuides,
}: GuideDetailProps) {
  // Find the guide in allGuides
  const guide = allGuides.find((g) => g.uuid === guideId);

  // Add styles to the document
  React.useEffect(() => {
    const style = document.createElement("style");
    style.textContent = styles;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  if (!guide) {
    return (
      <div className="guide-detail-container">
        <div className="error-message">
          <h2>Guide Not Found</h2>
          <p>The requested guide could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="guide-detail-container">
      <div className="guide-header">
        <div className="header-top-row">
          <button className="back-button" onClick={onBack}>
            ← Back to Pin
          </button>
          <button
            className="open-browser-button"
            onClick={async () => {
              const baseUrl = await getWebsiteUrl();
              console.log("baseUrl");
              console.log(baseUrl);
              window.open(`${baseUrl}/guide/${guide.uuid}`, "_blank");
            }}
            title="Open in browser"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 3h6v6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M10 14L21 3"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span>Open in Browser</span>
          </button>
        </div>
        <h1 className="guide-title">{guide.title}</h1>
        <div className="guide-meta">
          <span className="author">By {guide.author_name}</span>
          <span className="date">
            Last updated: {new Date(guide.updated_at).toLocaleDateString()}
          </span>
        </div>
      </div>

      <div className="guide-content">
        <div className="guide-steps">
          <h2>Steps</h2>
          {guide.steps?.map((step, index) => (
            <div key={index} className="guide-step">
              <div className="step-number">{index + 1}</div>
              <div className="step-content">
                <p>{step.description}</p>
                {step.screenshot && (
                  <div className="step-screenshot">
                    <img src={step.screenshot} alt={`Step ${index + 1}`} />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Open in Browser button moved to header */}
    </div>
  );
}
