import React, { useState, useEffect, useRef } from "react";
import styles from "data-text:../styles/video-guide-detail.css";
import { getWebsiteUrl } from "~utils/domain-config";
import { sanitizeTitle } from "~utils/sanitize-title";
import fullscreenIcon from "data-base64:~assets/fullscreen.svg";
interface VideoGuideStep {
  step: number;
  title: string;
  content: string;
  startTime: string;
  endTime: string;
  summary: string;
}

interface VideoGuideResponse {
  id: number;
  user_email: string;
  srt_url: string;
  video: string;
  response: {
    steps: VideoGuideStep[];
  };
  title: string;
  thumbnail_image: string;
  edit_access: boolean;
  delete_access: boolean;
}

interface VideoGuideDetailProps {
  guideId: string;
  onBack: () => void;
  allGuides: VideoGuideResponse[];
}

interface VideoCache {
  [key: string]: {
    url: string;
    blob: Blob;
    isLoading: boolean;
    error?: string;
  };
}

interface SrtEntry {
  id: number;
  startTime: string;
  endTime: string;
  text: string;
}

export default function VideoGuideDetail({
  guideId,
  onBack,
  allGuides,
}: VideoGuideDetailProps) {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [videoState, setVideoState] = useState<{
    url: string | null;
    isLoading: boolean;
    error?: string;
  }>({ url: null, isLoading: true });
  const [progressState, setProgressState] = useState<{
    [key: number]: {
      progress: number;
      currentTime: number;
    };
  }>({});
  const [volumeLevel, setVolumeLevel] = useState<number>(1);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [srtData, setSrtData] = useState<SrtEntry[]>([]);
  const [isSrtLoading, setIsSrtLoading] = useState<boolean>(false);
  const [srtError, setSrtError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<{
    [key: number]: "summary" | "transcription";
  }>({});
  const abortControllerRef = useRef<AbortController | null>(null);
  const videoRefs = useRef<{ [key: number]: HTMLVideoElement | null }>({});
  const videoContainerRefs = useRef<{ [key: number]: HTMLDivElement | null }>(
    {}
  );

  // Find the guide in allGuides
  const guide = allGuides.find((g) => g.id.toString() === guideId);

  // Add styles to the document
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = styles;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Parse SRT file content
  const parseSrtContent = (content: string): SrtEntry[] => {
    const entries: SrtEntry[] = [];
    const blocks = content.trim().split(/\n\s*\n/);

    blocks.forEach((block) => {
      const lines = block.trim().split("\n");
      if (lines.length >= 3) {
        const id = parseInt(lines[0].trim(), 10);
        const timeMatch = lines[1].match(
          /(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/
        );

        if (timeMatch) {
          const startTime = timeMatch[1];
          const endTime = timeMatch[2];
          const text = lines.slice(2).join("\n");

          entries.push({
            id,
            startTime,
            endTime,
            text,
          });
        }
      }
    });

    return entries;
  };

  // Get transcription for a specific step
  const getStepTranscription = (step: VideoGuideStep): string => {
    if (!srtData.length) return "Transcription not available";

    const startSeconds = convertTimeToSeconds(step.startTime);
    const endSeconds = convertTimeToSeconds(step.endTime);

    const relevantEntries = srtData.filter((entry) => {
      const entryStartSeconds = convertTimeToSeconds(entry.startTime);
      const entryEndSeconds = convertTimeToSeconds(entry.endTime);

      // Check if there's any overlap between the step time range and the entry time range
      return entryStartSeconds <= endSeconds && entryEndSeconds >= startSeconds;
    });

    if (relevantEntries.length === 0)
      return "No transcription available for this segment";

    return relevantEntries.map((entry) => entry.text).join("\n");
  };

  // Fetch video and SRT once when component mounts
  useEffect(() => {
    if (!guide) return;

    const fetchVideo = async () => {
      try {
        console.log("Starting video fetch");
        const fetchStartTime = Date.now();

        abortControllerRef.current = new AbortController();
        const response = (await Promise.race([
          fetch(guide.video, {
            signal: abortControllerRef.current.signal,
          }),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Fetch timeout after 30s")),
              30000
            )
          ),
        ])) as Response;

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log(
          `Fetch completed in ${(Date.now() - fetchStartTime) / 1000}s`
        );

        console.log("Starting blob conversion");
        const blobStartTime = Date.now();
        const videoBlob = await response.blob();
        console.log(
          `Blob conversion completed in ${(Date.now() - blobStartTime) / 1000}s`
        );

        const url = URL.createObjectURL(videoBlob);
        setVideoState({ url, isLoading: false });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        console.error("Error loading video:", errorMessage);
        setVideoState({ url: null, isLoading: false, error: errorMessage });
      }
    };

    const fetchSrt = async () => {
      if (!guide.srt_url) return;

      setIsSrtLoading(true);
      setSrtError(null);

      try {
        const response = await fetch(guide.srt_url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const srtContent = await response.text();
        const parsedSrt = parseSrtContent(srtContent);
        setSrtData(parsedSrt);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        console.error("Error loading SRT file:", errorMessage);
        setSrtError(errorMessage);
      } finally {
        setIsSrtLoading(false);
      }
    };

    // Initialize all steps to show summary tab by default
    const initialTabs: { [key: number]: "summary" | "transcription" } = {};
    guide.response.steps.forEach((_, index) => {
      initialTabs[index] = "summary";
    });
    setActiveTab(initialTabs);

    fetchVideo();
    fetchSrt();

    return () => {
      if (videoState.url) {
        URL.revokeObjectURL(videoState.url);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [guide]);

  const formatTime = (timeStr: string) => {
    if (!timeStr || timeStr === "00:00") {
      return "00:00";
    }
    try {
      const [hours, minutes, seconds] = timeStr.split(":");
      if (!hours || !minutes || !seconds) {
        return "00:00";
      }
      return `${hours}:${minutes}:${seconds.split(",")[0]}`;
    } catch (error) {
      console.error("Error formatting time:", error);
      return "00:00";
    }
  };

  const convertTimeToSeconds = (timeStr: string): number => {
    if (!timeStr) {
      return 0;
    }
    try {
      const [hours, minutes, seconds] = timeStr.split(":");
      if (!hours || !minutes || !seconds) {
        return 0;
      }
      return (
        parseInt(hours) * 3600 +
        parseInt(minutes) * 60 +
        parseFloat(seconds.replace(",", "."))
      );
    } catch (error) {
      console.error("Error converting time to seconds:", error);
      return 0;
    }
  };

  const formatTimeDisplay = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };

  const handleVideoTimeUpdate = (
    e: React.SyntheticEvent<HTMLVideoElement>,
    step: VideoGuideStep,
    index: number
  ) => {
    if (!step.startTime || !step.endTime) {
      console.warn("Missing time data for step", step);
      return;
    }

    const video = e.currentTarget;
    const currentTime = video.currentTime;
    const startTime = convertTimeToSeconds(step.startTime);
    const endTime = convertTimeToSeconds(step.endTime);

    // Check for valid time range
    if (startTime >= endTime) {
      console.warn("Invalid time range for step", step);
      return;
    }

    // Calculate progress as percentage of current segment
    const segmentDuration = endTime - startTime;
    const segmentProgress = ((currentTime - startTime) / segmentDuration) * 100;
    const clampedProgress = Math.max(0, Math.min(100, segmentProgress));

    setProgressState((prev) => ({
      ...prev,
      [index]: {
        progress: clampedProgress,
        currentTime: currentTime - startTime,
      },
    }));

    if (currentTime < startTime) {
      video.currentTime = startTime;
    } else if (currentTime > endTime) {
      video.pause();
      video.currentTime = startTime;
      setProgressState((prev) => ({
        ...prev,
        [index]: {
          progress: 0,
          currentTime: 0,
        },
      }));
    }
  };

  const playSegment = (index: number) => {
    setCurrentStep(index);
    const step = guide?.response.steps[index];
    const video = videoRefs.current[index];

    if (video && step && step.startTime) {
      if (video.currentTime > convertTimeToSeconds(step.endTime)) {
        video.currentTime = convertTimeToSeconds(step.startTime);
      }
      if (video.currentTime < convertTimeToSeconds(step.startTime)) {
        video.currentTime = convertTimeToSeconds(step.startTime);
      }

      video.play().catch((err) => console.error("Error playing video:", err));
    }
  };

  const togglePlayPause = (index: number) => {
    const video = videoRefs.current[index];
    if (!video) return;

    if (video.paused) {
      playSegment(index);
    } else {
      video.pause();
    }
  };

  const handleSeek = (index: number, e: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = e.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const step = guide?.response.steps[index];
    const video = videoRefs.current[index];

    if (!video || !step || !step.startTime || !step.endTime) {
      return;
    }

    const startTime = convertTimeToSeconds(step.startTime);
    const endTime = convertTimeToSeconds(step.endTime);

    // Check for valid time range
    if (startTime >= endTime) {
      console.warn("Invalid time range for step", step);
      return;
    }

    const segmentDuration = endTime - startTime;
    const newTime = startTime + clickPosition * segmentDuration;

    video.currentTime = newTime;

    // Update progress immediately for visual feedback
    setProgressState((prev) => ({
      ...prev,
      [index]: {
        progress: clickPosition * 100,
        currentTime: clickPosition * segmentDuration,
      },
    }));
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolumeLevel(newVolume);
    setIsMuted(newVolume === 0);

    // Apply to all videos
    Object.values(videoRefs.current).forEach((video) => {
      if (video) {
        video.volume = newVolume;
        video.muted = newVolume === 0;
      }
    });
  };

  const toggleMute = () => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);

    // Apply to all videos
    Object.values(videoRefs.current).forEach((video) => {
      if (video) {
        video.muted = newMuted;
      }
    });
  };

  const toggleFullscreen = (index: number) => {
    const video = videoRefs.current[index];
    if (!video) return;

    if (video.requestFullscreen) {
      console.log("Requesting fullscreen");
      video.requestFullscreen();
    }
  };

  // Toggle between summary and transcription tabs
  const toggleTab = (index: number, tab: "summary" | "transcription") => {
    setActiveTab((prev) => ({
      ...prev,
      [index]: tab,
    }));
  };

  if (!guide) {
    return (
      <div className="video-guide-detail-container">
        <div className="error-message">
          <h2>Guide Not Found</h2>
          <p>The requested guide could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="video-guide-detail-container">
      <div className="video-guide-header">
        <div className="header-top-row">
          <button className="back-button" onClick={onBack}>
            ← Back to Pin
          </button>
          <button
            className="open-browser-button"
            onClick={async () => {
              const baseUrl = await getWebsiteUrl();
              window.open(`${baseUrl}/guide/videoguide/${guide.id}`, "_blank");
            }}
            title="Open in browser"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 3h6v6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M10 14L21 3"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span>Open in Browser</span>
          </button>
        </div>
        <h1 className="video-guide-title">{sanitizeTitle(guide.title)}</h1>
        <div className="video-guide-meta">
          <span className="author">By {guide.user_email}</span>
        </div>
      </div>

      <div className="video-steps-container">
        <h2>Steps</h2>
        <div className="video-steps-list">
          {guide.response.steps.map((step, index) => {
            const { progress = 0, currentTime = 0 } = progressState[index] || {
              progress: 0,
              currentTime: 0,
            };

            // Safely handle time values
            const startTime = step.startTime
              ? convertTimeToSeconds(step.startTime)
              : 0;
            const endTime = step.endTime
              ? convertTimeToSeconds(step.endTime)
              : 0;
            const segmentDuration = Math.max(0, endTime - startTime);

            const video = videoRefs.current[index];
            const isPlaying = video && !video.paused && !video.ended;

            return (
              <div
                key={index}
                className={`video-step ${currentStep === index ? "active" : ""}`}
              >
                <div className="step-content">
                  <div className="step-header">
                    <div className="step-number">{step.step}</div>
                    <div>
                      <h3 className="step-title">{step.title}</h3>
                    </div>
                  </div>

                  <div
                    className="step-video-container"
                    ref={(el) => (videoContainerRefs.current[index] = el)}
                  >
                    {videoState.isLoading && (
                      <div className="video-loading-overlay">
                        <div className="loading-spinner"></div>
                        <div className="loading-text">Loading video...</div>
                      </div>
                    )}
                    {videoState.error && (
                      <div className="error-message">
                        <p>Error loading video: {videoState.error}</p>
                        <button
                          className="retry-button"
                          onClick={() => {
                            setVideoState((prev) => ({
                              ...prev,
                              isLoading: true,
                              error: undefined,
                            }));
                            // Re-fetch the video
                            const fetchVideo = async () => {
                              try {
                                console.log("Retrying video fetch");

                                if (abortControllerRef.current) {
                                  abortControllerRef.current.abort();
                                }

                                abortControllerRef.current =
                                  new AbortController();
                                const response = (await Promise.race([
                                  fetch(guide.video, {
                                    signal: abortControllerRef.current.signal,
                                  }),
                                  new Promise((_, reject) =>
                                    setTimeout(
                                      () =>
                                        reject(
                                          new Error("Fetch timeout after 30s")
                                        ),
                                      30000
                                    )
                                  ),
                                ])) as Response;

                                if (!response.ok) {
                                  throw new Error(
                                    `HTTP error! status: ${response.status}`
                                  );
                                }

                                const videoBlob = await response.blob();
                                const url = URL.createObjectURL(videoBlob);
                                setVideoState({ url, isLoading: false });
                              } catch (error) {
                                const errorMessage =
                                  error instanceof Error
                                    ? error.message
                                    : "Unknown error";
                                console.error(
                                  "Error loading video:",
                                  errorMessage
                                );
                                setVideoState({
                                  url: null,
                                  isLoading: false,
                                  error: errorMessage,
                                });
                              }
                            };

                            fetchVideo();
                          }}
                        >
                          Retry
                        </button>
                      </div>
                    )}
                    <>
                      <video
                        ref={(el) => (videoRefs.current[index] = el)}
                        className="step-video"
                        src={videoState.url}
                        poster={guide.thumbnail_image}
                        onPlay={() => setCurrentStep(index)}
                        onTimeUpdate={(e) =>
                          handleVideoTimeUpdate(e, step, index)
                        }
                        preload="metadata"
                        onClick={() => togglePlayPause(index)}
                      />

                      <div className="video-controls">
                        <button
                          className="play-pause-button"
                          onClick={() => togglePlayPause(index)}
                        >
                          {isPlaying ? (
                            <svg
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M8 5V19M16 5V19"
                                stroke="white"
                                strokeWidth="2.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          ) : (
                            <svg
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path d="M6 4L20 12L6 20V4Z" fill="white" />
                            </svg>
                          )}
                        </button>

                        <div className="time-display">
                          {formatTimeDisplay(currentTime)}
                        </div>

                        <div
                          className="progress-track"
                          onClick={(e) => handleSeek(index, e)}
                        >
                          <div
                            className="progress-bar"
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>

                        <div className="time-display">
                          {formatTimeDisplay(segmentDuration)}
                        </div>

                        <div className="volume-control">
                          <button
                            className="volume-button"
                            onClick={toggleMute}
                          >
                            {isMuted ? "🔇" : volumeLevel > 0.5 ? "🔊" : "🔉"}
                          </button>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.05"
                            value={volumeLevel}
                            onChange={handleVolumeChange}
                            className="volume-slider"
                          />
                        </div>
                      </div>
                    </>
                  </div>

                  <div className="step-content-tabs">
                    <div className="step-tabs-header">
                      <button
                        className={`step-tab-button ${activeTab[index] === "summary" ? "active" : ""}`}
                        onClick={() => toggleTab(index, "summary")}
                      >
                        Summary
                      </button>
                      <button
                        className={`step-tab-button ${activeTab[index] === "transcription" ? "active" : ""}`}
                        onClick={() => toggleTab(index, "transcription")}
                      >
                        Transcription
                      </button>
                    </div>

                    <div className="step-tab-content">
                      {activeTab[index] === "summary" ? (
                        <div className="step-summary">
                          {step.summary ||
                            "No summary available for this step."}
                        </div>
                      ) : (
                        <div className="step-transcription">
                          <div className="step-time">
                            {formatTime(step.startTime)} -{" "}
                            {formatTime(step.endTime)}
                          </div>
                          {isSrtLoading ? (
                            <div className="loading-text">
                              Loading transcription...
                            </div>
                          ) : srtError ? (
                            <div className="error-text">
                              Error loading transcription: {srtError}
                            </div>
                          ) : (
                            getStepTranscription(step)
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Open in Browser button moved to header */}
    </div>
  );
}
