import React, { useState, useEffect } from "react";
import styles from "data-text:../styles/knowledge-capture-detail.css";
import { getWebsiteUrl } from "~utils/domain-config";
import type { KnowledgeCaptureResponse } from "~/background/api";

interface KnowledgeCaptureDetailProps {
  captureId: string;
  onBack: () => void;
  allKnowledgeCaptures: KnowledgeCaptureResponse[];
}

export default function KnowledgeCaptureDetail({
  captureId,
  onBack,
  allKnowledgeCaptures,
}: KnowledgeCaptureDetailProps) {
  // Find the knowledge capture in allKnowledgeCaptures
  const knowledgeCapture = allKnowledgeCaptures.find(
    (k) => k.uuid === captureId
  );

  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Add styles to the document
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = styles;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Handle zoom in
  const handleZoomIn = () => {
    setZoomLevel((prevZoom) => Math.min(prevZoom + 0.25, 3));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setZoomLevel((prevZoom) => Math.max(prevZoom - 0.25, 0.5));
  };

  // Handle reset zoom
  const handleResetZoom = () => {
    setZoomLevel(1);
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    // Reset zoom when toggling fullscreen
    setZoomLevel(1);
  };

  // Format date
  const formatDate = (dateStr: string): string => {
    return new Date(dateStr).toLocaleString();
  };

  if (!knowledgeCapture) {
    return (
      <div className="knowledge-detail-container">
        <div className="error-message">
          <h2>Knowledge Capture Not Found</h2>
          <p>The requested knowledge capture could not be found.</p>
          <button className="back-button" onClick={onBack}>
            ← Back to Pin
          </button>
        </div>
      </div>
    );
  }

  // Render fullscreen view
  if (isFullscreen) {
    return (
      <div className="fullscreen">
        <button className="exit-fullscreen-button" onClick={toggleFullscreen}>
          Exit Fullscreen
        </button>
        <div className="image-controls">
          <button
            className="image-control-button"
            onClick={handleZoomIn}
            title="Zoom In"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
              <line x1="11" y1="8" x2="11" y2="14" />
              <line x1="8" y1="11" x2="14" y2="11" />
            </svg>
          </button>
          <button
            className="image-control-button"
            onClick={handleZoomOut}
            title="Zoom Out"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
              <line x1="8" y1="11" x2="14" y2="11" />
            </svg>
          </button>
          <button
            className="image-control-button"
            onClick={handleResetZoom}
            title="Reset Zoom"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 12a9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9 9 9 0 0 1 9 9z" />
              <path d="M9 12h6" />
            </svg>
          </button>
        </div>
        <img
          src={knowledgeCapture.screenshot}
          alt="Knowledge Capture"
          className="knowledge-image"
          style={{ transform: `scale(${zoomLevel})` }}
        />
      </div>
    );
  }

  return (
    <div className="knowledge-detail-container">
      <div className="knowledge-header">
        <div className="header-top-row">
          <button className="back-button" onClick={onBack}>
            ← Back to Pin
          </button>
          <button
            className="open-browser-button"
            onClick={async () => {
              const baseUrl = await getWebsiteUrl();
              window.open(
                `${baseUrl}/documents?capture=${knowledgeCapture.uuid}`,
                "_blank"
              );
            }}
            title="Open in browser"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 3h6v6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M10 14L21 3"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span>Open in Browser</span>
          </button>
        </div>
        <h1 className="knowledge-title">
          {knowledgeCapture.description || "Knowledge Capture"}
        </h1>
      </div>

      <div className="knowledge-content">
        <div className="knowledge-image-container">
          <div
            className="knowledge-image-wrapper"
            style={{ transform: `scale(${zoomLevel})` }}
          >
            <img
              src={knowledgeCapture.screenshot}
              alt="Knowledge Capture"
              className="knowledge-image"
            />
          </div>
          <div className="image-controls">
            <button
              className="image-control-button"
              onClick={toggleFullscreen}
              title="Fullscreen"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
              </svg>
            </button>
            <button
              className="image-control-button"
              onClick={handleZoomIn}
              title="Zoom In"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="11" cy="11" r="8" />
                <line x1="21" y1="21" x2="16.65" y2="16.65" />
                <line x1="11" y1="8" x2="11" y2="14" />
                <line x1="8" y1="11" x2="14" y2="11" />
              </svg>
            </button>
            <button
              className="image-control-button"
              onClick={handleZoomOut}
              title="Zoom Out"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="11" cy="11" r="8" />
                <line x1="21" y1="21" x2="16.65" y2="16.65" />
                <line x1="8" y1="11" x2="14" y2="11" />
              </svg>
            </button>
            <button
              className="image-control-button"
              onClick={handleResetZoom}
              title="Reset Zoom"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21 12a9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9 9 9 0 0 1 9 9z" />
                <path d="M9 12h6" />
              </svg>
            </button>
          </div>
        </div>
        <div className="pin-metadata">
          <div className="metadata-section">
            <h3>Capture Details</h3>
            {/* Creator and Created At Information */}
            {(knowledgeCapture.creator_email ||
              knowledgeCapture.created_at ||
              knowledgeCapture.current_url) && (
              <div className="creator-info">
                {knowledgeCapture.current_url && (
                  <div className="creator-row">
                    <span className="creator-label">domain</span>
                    <a
                      className="creator-value"
                      title={knowledgeCapture.current_url}
                      href={knowledgeCapture.current_url}
                    >
                      {new URL(knowledgeCapture.current_url).hostname}
                    </a>
                  </div>
                )}
                {knowledgeCapture.creator_email && (
                  <div className="creator-row">
                    <span className="creator-label">Created by:</span>
                    <span className="creator-value">
                      {knowledgeCapture.creator_email}
                    </span>
                  </div>
                )}
                {knowledgeCapture.created_at && (
                  <div className="creator-row">
                    <span className="creator-label">Created on:</span>
                    <span className="creator-value">
                      {formatDate(knowledgeCapture.created_at)}
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
