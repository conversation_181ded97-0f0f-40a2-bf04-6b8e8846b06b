import React from "react";
import "../styles/pin-popup.css"; // We'll reuse the pin-popup styles

export default function UpdateNotification() {
  const handleUpdate = () => {
    // Reload the extension to apply the update
    // https://chromewebstore.google.com/detail/talknician/bhemfnngnbjjhhpgljplaiegbcleiiif
    chrome.runtime.sendMessage({
      action: "openTalknician",
      url: "https://chromewebstore.google.com/detail/talknician/bhemfnngnbjjhhpgljplaiegbcleiiif",
    });
  };

  return (
    <div className="pin-popup">
      <div className="popup-content">
        <div className="header">
          <img
            src={chrome.runtime.getURL("assets/icon.png")}
            alt="Talknician"
            className="extension-icon"
          />
          <h1>Update Available!</h1>
        </div>
        <p className="message">A new version of Talknician is available 🎉</p>
        <button
          onClick={handleUpdate}
          className="update-button"
          style={{
            padding: "8px 16px",
            backgroundColor: "#007AFF",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            marginTop: "16px",
          }}
        >
          Update Now
        </button>
      </div>
    </div>
  );
}
