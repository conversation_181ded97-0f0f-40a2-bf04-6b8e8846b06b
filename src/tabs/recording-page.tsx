import React, { useCallback, useEffect, useRef, useState } from "react";
import { useRecordingState } from "~hooks/useRecordingState";
import type { PlasmoGetStyle } from "plasmo";
import styleText from "data-text:~styles/globals.css";
import "../styles/globals.css";
import { LogoIcon } from "~components/icons";
import { useStorage } from "@plasmohq/storage/hook";
import {
  recordingStorage,
  defaultRecordingState,
  type RecordingState,
} from "~storage/recording-storage";

/**
 * Style injection for Plasmo extension
 */
export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style");
  style.textContent = styleText;
  return style;
};

interface RecordingData {
  stream: MediaStream | null;
  mediaRecorder: MediaRecorder | null;
  chunks: Blob[];
  recordedBlob: Blob | null;
  recordedUrl: string | null;
  hasAudio: boolean;
}

export default function RecordingPage() {
  const [recordingState, setRecordingState] = useStorage<RecordingState>(
    {
      key: "recording-state",
      instance: recordingStorage,
    },
    defaultRecordingState
  );

  const [recordingData, setRecordingData] = useState<RecordingData>({
    stream: null,
    mediaRecorder: null,
    chunks: [],
    recordedBlob: null,
    recordedUrl: null,
    hasAudio: false,
  });
  const [error, setError] = useState<string | null>(null);
  type RecordingStatus =
    | "idle"
    | "starting"
    | "recording"
    | "paused"
    | "stopped"
    | "uploading"
    | "uploaded"
    | "error";
  const [status, setStatus] = useState<RecordingStatus>("idle");
  const videoRef = useRef<HTMLVideoElement>(null);

  // No need for additional styles as we're using Tailwind

  // Listen for messages from the background script
  useEffect(() => {
    const messageListener = (
      message: any,
      _sender: chrome.runtime.MessageSender,
      sendResponse: (response?: any) => void
    ) => {
      console.log("Recording page received message:", message);

      if (message.action === "startRecording") {
        startRecording(sendResponse);
        return true;
      } else if (message.action === "pauseRecording") {
        pauseRecording(sendResponse);
        return true;
      } else if (message.action === "resumeRecording") {
        resumeRecording(sendResponse);
        return true;
      } else if (message.action === "stopRecording") {
        stopRecording(sendResponse);
        return true;
      } else if (message.action === "resetRecording") {
        resetRecording(sendResponse);
        return true;
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, []); // Remove the dependency on recordingData to prevent re-registering the listener

  // Watch for recording state changes
  useEffect(() => {
    if (recordingState.isRecording && status === "idle") {
      // Auto-start recording when the recording state changes to isRecording
      startRecording();
    } else if (!recordingState.isRecording && status === "recording") {
      // Auto-stop recording when the recording state changes to not isRecording
      stopRecording();
    } else if (recordingState.isPaused && status === "recording") {
      // Auto-pause recording when the recording state changes to isPaused
      pauseRecording();
    } else if (!recordingState.isPaused && status === "paused") {
      // Auto-resume recording when the recording state changes to not isPaused
      resumeRecording();
    }

    // Log the current recording time for debugging
    console.log("Current recording time:", recordingState.recordingTime);
  }, [recordingState, status]);

  // Add a global visibility change listener to detect when the user stops sharing
  useEffect(() => {
    const handleVisibilityChange = () => {
      console.log("Document visibility changed:", document.visibilityState);

      // If the document becomes hidden and we're recording, it might be because
      // the user clicked "Stop sharing" in Chrome's UI
      if (document.visibilityState === "hidden" && status === "recording") {
        console.log(
          "Document hidden while recording, checking if we need to stop"
        );

        // Check if the stream is still active
        if (recordingData.stream) {
          const videoTracks = recordingData.stream.getVideoTracks();
          if (videoTracks.length === 0 || !videoTracks[0].enabled) {
            console.log("Video track not enabled, stopping recording");

            // Update the global recording state first
            chrome.runtime.sendMessage(
              {
                action: "stopRecording",
              },
              () => {
                console.log(
                  "Sent stopRecording message to background from visibility change event"
                );

                // Then stop our local recording
                stopRecording();
              }
            );
          }
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [status, recordingData.stream]);

  // Create refs outside the effect to persist across component re-renders
  const timerRef = useRef<number | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const pausedTimeRef = useRef<number>(0);
  const pauseStartTimeRef = useRef<number | null>(null);
  const lastUpdateTimeRef = useRef<number>(0);

  // Function to update the timer display
  const updateTimerDisplay = useCallback(() => {
    // If we haven't started recording yet, don't update
    if (startTimeRef.current === null) return;

    const now = Date.now();
    const elapsedTime = now - startTimeRef.current - pausedTimeRef.current;
    const seconds = Math.floor(elapsedTime / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    // Format time as MM:SS
    const formattedTime = `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;

    // Only log occasionally to reduce console spam
    if (now - lastUpdateTimeRef.current > 1000) {
      console.log(
        `Timer update: ${formattedTime}, elapsed: ${elapsedTime}ms, paused: ${pausedTimeRef.current}ms`
      );
      lastUpdateTimeRef.current = now;
    }

    // Update the global recording state with the new time
    setRecordingState({
      ...recordingState,
      recordingTime: formattedTime,
    });

    // Update the UI directly
    const timerElement = document.querySelector(".timer-display");
    if (timerElement) {
      timerElement.textContent = formattedTime;
    }
  }, [recordingState]);

  // Reset timer values when starting a new recording
  useEffect(() => {
    if (status === "starting") {
      console.log("Resetting timer values for new recording");
      startTimeRef.current = null; // Will be set when recording actually starts
      pausedTimeRef.current = 0;
      pauseStartTimeRef.current = null;

      // Reset the timer display to 00:00
      setRecordingState({
        ...recordingState,
        recordingTime: "00:00",
      });

      // Update the UI directly
      const timerElement = document.querySelector(".timer-display");
      if (timerElement) {
        timerElement.textContent = "00:00";
      }
    }
  }, [status, recordingState]);

  // Set the start time when recording actually starts
  useEffect(() => {
    if (status === "recording" && startTimeRef.current === null) {
      console.log("Initializing start time for recording");
      startTimeRef.current = Date.now();
    }
  }, [status]);

  // Add a local timer for the recording page
  useEffect(() => {
    console.log(`Timer effect running, status: ${status}`);

    if (status === "recording") {
      console.log("Starting/resuming local timer");

      // If we're resuming from a pause, calculate the additional pause time
      if (pauseStartTimeRef.current !== null) {
        const additionalPauseTime = Date.now() - pauseStartTimeRef.current;
        pausedTimeRef.current += additionalPauseTime;
        pauseStartTimeRef.current = null;
        console.log(
          `Resumed timer, added ${additionalPauseTime}ms to pause time, total paused: ${pausedTimeRef.current}ms`
        );
      }

      // Update the timer immediately to avoid the initial delay
      updateTimerDisplay();

      // Start or resume the timer with a more frequent update interval
      // This makes the timer more accurate and responsive
      timerRef.current = window.setInterval(updateTimerDisplay, 100);
    } else if (status === "paused") {
      console.log("Pausing timer");

      // Record the time when we paused
      pauseStartTimeRef.current = Date.now();

      // Clear the interval
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    } else if (status === "stopped" || status === "idle") {
      console.log("Stopping timer");

      // Clear the interval
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Reset pause tracking
      pauseStartTimeRef.current = null;
    }

    // Cleanup function
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [status, updateTimerDisplay]);

  // Watch for changes to the stream and set up listeners
  useEffect(() => {
    if (recordingData.stream && status === "recording") {
      console.log("Setting up stream event listeners");

      // Listen for the 'inactive' event on the stream
      recordingData.stream.addEventListener("inactive", () => {
        console.log("STREAM INACTIVE EVENT - Stream became inactive");
        if (status === "recording") {
          console.log("Stopping recording due to inactive stream");

          // Update the global recording state first
          chrome.runtime.sendMessage(
            {
              action: "stopRecording",
            },
            () => {
              console.log(
                "Sent stopRecording message to background from stream inactive event"
              );

              // Then stop our local recording
              stopRecording();
            }
          );
        } else {
          // Even if we're not recording, make sure the global state is updated
          chrome.runtime.sendMessage({ action: "stopRecording" });
        }
      });

      // Check each track for ended events
      recordingData.stream.getTracks().forEach((track) => {
        track.addEventListener("ended", () => {
          console.log(`TRACK ENDED EVENT - Track ${track.kind} ended`);
          if (status === "recording") {
            console.log("Stopping recording due to ended track");

            // Update the global recording state first
            chrome.runtime.sendMessage(
              {
                action: "stopRecording",
              },
              () => {
                console.log(
                  `Sent stopRecording message to background from track ${track.kind} ended event`
                );

                // Then stop our local recording
                stopRecording();
              }
            );
          } else {
            // Even if we're not recording, make sure the global state is updated
            chrome.runtime.sendMessage({ action: "stopRecording" });
          }
        });
      });
    }
  }, [recordingData.stream, status]);

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (recordingData.stream) {
        recordingData.stream.getTracks().forEach((track) => {
          try {
            track.stop();
          } catch (e) {
            console.error("Error stopping track during cleanup:", e);
          }
        });
      }
      if (recordingData.recordedUrl) {
        URL.revokeObjectURL(recordingData.recordedUrl);
      }
    };
  }, [recordingData]);

  const startRecording = async (sendResponse?: (response?: any) => void) => {
    try {
      setError(null);
      setStatus("starting");

      // Get the display media (screen or tab) - system audio is always enabled
      const displayStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true, // Always enable system audio
      });

      // Get microphone audio - mic is always enabled
      let audioStream: MediaStream | null = null;
      try {
        audioStream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
      } catch (err) {
        console.error("Error getting microphone:", err);
        // Continue without mic audio
      }

      // Combine streams if we have audio
      let combinedStream: MediaStream;
      if (audioStream) {
        combinedStream = new MediaStream([
          ...displayStream.getVideoTracks(),
          ...audioStream.getAudioTracks(),
          ...displayStream.getAudioTracks(),
        ]);
      } else {
        combinedStream = displayStream;
      }

      // Check if we have any audio tracks
      const hasAudio = combinedStream.getAudioTracks().length > 0;

      let mimeType = "";
      const preferredTypes = [
        "video/mp4",
        "video/mp4;codecs=h264,aac",
        "video/webm;codecs=h264,opus",
        "video/webm;codecs=vp9,opus",
        "video/webm;codecs=vp8,opus",
        "video/webm",
      ];

      for (const type of preferredTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          mimeType = type;
          break;
        }
      }
      // Create media recorder
      const options = { mimeType: mimeType };
      const mediaRecorder = new MediaRecorder(combinedStream, options);

      // Set up event handlers
      const chunks: Blob[] = [];
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data);
        }
      };

      // Handle errors in the MediaRecorder
      mediaRecorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
        setError(`MediaRecorder error: ${event.error}`);

        // Try to stop the recording if there's an error
        if (mediaRecorder.state !== "inactive") {
          console.log("Stopping recording due to MediaRecorder error");
          stopRecording();
        }
      };

      // Handle unexpected stops
      mediaRecorder.onstop = () => {
        console.log("MediaRecorder stopped unexpectedly");

        // If we're not already in stopped state, handle the stop
        if (status !== "stopped") {
          console.log("Handling unexpected MediaRecorder stop");
          setStatus("stopped");

          // Update the global recording state to reflect that recording has stopped
          // This will ensure the recording controls UI gets updated
          chrome.runtime.sendMessage(
            {
              action: "stopRecording",
            },
            () => {
              console.log(
                "Sent stopRecording message to background to update global state"
              );
            }
          );

          // Notify the background script to activate the recording tab
          chrome.runtime.sendMessage({ action: "recordingStoppedFromPage" });
        }
      };

      // Handle the case when user clicks "Stop sharing" in Chrome's UI
      const videoTrack = displayStream.getVideoTracks()[0];
      console.log(
        "Setting up ended event listener on video track:",
        videoTrack
      );

      videoTrack.addEventListener("ended", () => {
        console.log(
          "VIDEO TRACK ENDED EVENT FIRED - User stopped sharing screen via Chrome UI"
        );
        // Stop our recording when user stops sharing
        if (mediaRecorder && mediaRecorder.state !== "inactive") {
          console.log("Stopping recording due to video track ended event");

          // Update the global recording state first
          chrome.runtime.sendMessage(
            {
              action: "stopRecording",
            },
            () => {
              console.log(
                "Sent stopRecording message to background from video track ended event"
              );

              // Then stop our local recording
              stopRecording();
            }
          );
        } else {
          // Even if the mediaRecorder is inactive, make sure the global state is updated
          chrome.runtime.sendMessage({ action: "stopRecording" });
        }
      });

      // Also listen for the Chrome-specific "inactive" event
      if (videoTrack.onended === undefined) {
        console.log("Using Chrome-specific inactive event listener");
        videoTrack.onended = () => {
          console.log(
            "VIDEO TRACK ONENDED EVENT FIRED - User stopped sharing screen via Chrome UI"
          );
          if (mediaRecorder && mediaRecorder.state !== "inactive") {
            console.log("Stopping recording due to video track onended event");

            // Update the global recording state first
            chrome.runtime.sendMessage(
              {
                action: "stopRecording",
              },
              () => {
                console.log(
                  "Sent stopRecording message to background from video track onended event"
                );

                // Then stop our local recording
                stopRecording();
              }
            );
          } else {
            // Even if the mediaRecorder is inactive, make sure the global state is updated
            chrome.runtime.sendMessage({ action: "stopRecording" });
          }
        };
      }

      // Add a global event listener for when tracks are removed
      displayStream.addEventListener("removetrack", (event) => {
        console.log("TRACK REMOVED FROM STREAM:", event);
        if (mediaRecorder && mediaRecorder.state !== "inactive") {
          console.log("Stopping recording due to track removal");

          // Update the global recording state first
          chrome.runtime.sendMessage(
            {
              action: "stopRecording",
            },
            () => {
              console.log(
                "Sent stopRecording message to background from removetrack event"
              );

              // Then stop our local recording
              stopRecording();
            }
          );
        } else {
          // Even if the mediaRecorder is inactive, make sure the global state is updated
          chrome.runtime.sendMessage({ action: "stopRecording" });
        }
      });

      // Start recording
      mediaRecorder.start(1000); // Collect data every second

      // Set the status to recording - this will trigger the timer to start
      setStatus("recording");

      // Update state
      setRecordingData({
        stream: combinedStream,
        mediaRecorder,
        chunks,
        recordedBlob: null,
        recordedUrl: null,
        hasAudio,
      });

      // Show the stream in the video element
      if (videoRef.current) {
        videoRef.current.srcObject = combinedStream;
      }

      // Notify the background script that recording has actually started
      chrome.runtime.sendMessage({
        action: "recordingActuallyStarted",
        hasAudio,
      });

      // Send success response
      if (sendResponse) {
        sendResponse({ success: true, hasAudio });
      }
    } catch (err) {
      console.error("Error starting recording:", err);
      setError(`Failed to start recording: ${err.message}`);
      setStatus("error");

      // If the user aborted the screen selection, notify the background script
      // to update the global recording state
      if (
        err.name === "AbortError" ||
        err.name === "NotAllowedError" ||
        err.message.includes("Permission denied")
      ) {
        console.log("User aborted screen selection, stopping recording");

        // First update the local state
        setRecordingData({
          stream: null,
          mediaRecorder: null,
          chunks: [],
          recordedBlob: null,
          recordedUrl: null,
          hasAudio: false,
        });

        // Update the global recording state to reflect that recording has stopped
        chrome.runtime.sendMessage(
          {
            action: "stopRecording",
          },
          () => {
            console.log(
              "Sent stopRecording message to background after user aborted screen selection"
            );

            // Also notify the background script to cancel the recording
            // This will close the recording tab
            chrome.runtime.sendMessage(
              {
                action: "cancelRecording",
              },
              (response) => {
                console.log("Cancel recording response after abort:", response);
              }
            );
          }
        );
      }

      if (sendResponse) {
        sendResponse({ success: false, error: err.message });
      }
    }
  };

  const pauseRecording = (sendResponse?: (response?: any) => void) => {
    try {
      if (
        recordingData.mediaRecorder &&
        recordingData.mediaRecorder.state === "recording"
      ) {
        recordingData.mediaRecorder.pause();
        setStatus("paused");
        if (sendResponse) {
          sendResponse({ success: true });
        }
      } else {
        if (sendResponse) {
          sendResponse({
            success: false,
            error: "No active recording to pause",
          });
        }
      }
    } catch (err) {
      console.error("Error pausing recording:", err);
      setError(`Failed to pause recording: ${err.message}`);
      if (sendResponse) {
        sendResponse({ success: false, error: err.message });
      }
    }
  };

  const resumeRecording = (sendResponse?: (response?: any) => void) => {
    try {
      if (
        recordingData.mediaRecorder &&
        recordingData.mediaRecorder.state === "paused"
      ) {
        // Resume the media recorder
        recordingData.mediaRecorder.resume();

        // Set the status to recording - this will trigger the timer to resume
        // The timer effect will calculate the pause duration and update the pausedTime
        setStatus("recording");

        if (sendResponse) {
          sendResponse({ success: true });
        }
      } else {
        if (sendResponse) {
          sendResponse({
            success: false,
            error: "No paused recording to resume",
          });
        }
      }
    } catch (err) {
      console.error("Error resuming recording:", err);
      setError(`Failed to resume recording: ${err.message}`);
      if (sendResponse) {
        sendResponse({ success: false, error: err.message });
      }
    }
  };

  const stopRecording = (sendResponse?: (response?: any) => void) => {
    try {
      console.log(
        "Stop recording called, current state:",
        recordingData.mediaRecorder?.state
      );

      // If we're already in stopped state, don't try to stop again
      if (status === "stopped") {
        console.log("Recording already stopped, not stopping again");
        if (sendResponse) {
          sendResponse({ success: true, alreadyStopped: true });
        }
        return;
      }

      if (
        recordingData.mediaRecorder &&
        recordingData.mediaRecorder.state !== "inactive"
      ) {
        // Save the current chunks in case they're needed
        const currentChunks = [...recordingData.chunks];

        // Set up onStop handler before stopping
        recordingData.mediaRecorder.onstop = () => {
          console.log("MediaRecorder onstop event fired");

          // Create a blob from the chunks
          const blob = new Blob(
            currentChunks.length > 0 ? currentChunks : recordingData.chunks,
            { type: "video/webm" }
          );
          const url = URL.createObjectURL(blob);
          console.log("Created blob:", blob.size, "bytes, URL:", url);

          // Check if the recording has audio
          const hasAudio = recordingData.hasAudio;
          console.log("Recording has audio:", hasAudio);

          // Update state with the recorded blob and URL
          setRecordingData((prev) => ({
            ...prev,
            recordedBlob: blob,
            recordedUrl: url,
          }));

          setStatus("stopped");
          console.log("Set status to stopped");

          // Stop all tracks
          if (recordingData.stream) {
            recordingData.stream.getTracks().forEach((track) => {
              track.stop();
              console.log("Stopped track:", track.kind);
            });
          }

          // Notify the background script that recording was stopped from the page
          // This will make the recording tab active to show the preview
          chrome.runtime.sendMessage({ action: "recordingStoppedFromPage" });

          // Send the blob to the background script for processing
          console.log("Sending processRecording message to background");
          chrome.runtime.sendMessage(
            {
              action: "processRecording",
              blob: blob,
              hasAudio: hasAudio,
            },
            (response) => {
              console.log("Received processRecording response:", response);
              if (sendResponse) {
                sendResponse({
                  success: true,
                  blobSize: blob.size,
                  hasAudio: hasAudio,
                  ...response,
                });
              }
            }
          );
        };

        // Stop the recording
        console.log("Stopping MediaRecorder");
        try {
          recordingData.mediaRecorder.stop();
        } catch (stopError) {
          console.error("Error stopping MediaRecorder:", stopError);

          // If we can't stop the recorder, at least try to process what we have
          const blob = new Blob(recordingData.chunks, { type: "video/webm" });
          if (blob.size > 0) {
            console.log("Processing existing chunks despite stop error");
            const url = URL.createObjectURL(blob);

            // Update state with what we have
            setRecordingData((prev) => ({
              ...prev,
              recordedBlob: blob,
              recordedUrl: url,
            }));

            setStatus("stopped");

            // Stop all tracks
            if (recordingData.stream) {
              recordingData.stream.getTracks().forEach((track) => {
                try {
                  track.stop();
                } catch (e) {
                  console.error("Error stopping track:", e);
                }
              });
            }

            // Notify background and process what we have
            chrome.runtime.sendMessage({ action: "recordingStoppedFromPage" });
            chrome.runtime.sendMessage(
              {
                action: "processRecording",
                blob: blob,
                hasAudio: recordingData.hasAudio,
              },
              (response) => {
                if (sendResponse) {
                  sendResponse({
                    success: true,
                    blobSize: blob.size,
                    hasAudio: recordingData.hasAudio,
                    ...response,
                  });
                }
              }
            );
          }
        }
      } else {
        console.log("No active recording to stop");

        // Even if there's no active recording, we should still try to process any chunks we have
        if (recordingData.chunks.length > 0) {
          console.log("No active recorder but we have chunks, processing them");
          const blob = new Blob(recordingData.chunks, { type: "video/webm" });
          const url = URL.createObjectURL(blob);

          setRecordingData((prev) => ({
            ...prev,
            recordedBlob: blob,
            recordedUrl: url,
          }));

          setStatus("stopped");

          // Stop any tracks that might still be active
          if (recordingData.stream) {
            recordingData.stream.getTracks().forEach((track) => {
              try {
                track.stop();
              } catch (e) {
                console.error("Error stopping track:", e);
              }
            });
          }

          // Notify background and process what we have
          chrome.runtime.sendMessage({ action: "recordingStoppedFromPage" });
          chrome.runtime.sendMessage({
            action: "processRecording",
            blob: blob,
            hasAudio: recordingData.hasAudio,
          });

          if (sendResponse) {
            sendResponse({ success: true });
          }
        } else {
          if (sendResponse) {
            sendResponse({
              success: false,
              error: "No active recording to stop",
            });
          }
        }
      }
    } catch (err) {
      console.error("Error stopping recording:", err);
      setError(`Failed to stop recording: ${err.message}`);
      setStatus("error");
      if (sendResponse) {
        sendResponse({ success: false, error: err.message });
      }
    }
  };

  const resetRecording = (sendResponse?: (response?: any) => void) => {
    try {
      // First stop the current recording if it's active
      if (
        recordingData.mediaRecorder &&
        recordingData.mediaRecorder.state !== "inactive"
      ) {
        recordingData.mediaRecorder.stop();

        // Stop all tracks
        if (recordingData.stream) {
          recordingData.stream.getTracks().forEach((track) => track.stop());
        }

        // Revoke the URL if it exists
        if (recordingData.recordedUrl) {
          URL.revokeObjectURL(recordingData.recordedUrl);
        }
      }

      // Reset the recording data
      setRecordingData({
        stream: null,
        mediaRecorder: null,
        chunks: [],
        recordedBlob: null,
        recordedUrl: null,
        hasAudio: false,
      });

      setStatus("idle");

      // Start a new recording
      startRecording(sendResponse);
    } catch (err) {
      console.error("Error resetting recording:", err);
      setError(`Failed to reset recording: ${err.message}`);
      setStatus("error");
      if (sendResponse) {
        sendResponse({ success: false, error: err.message });
      }
    }
  };

  // Function to handle uploading the recording
  const handleUpload = async () => {
    if (recordingData.recordedBlob) {
      try {
        console.log("Uploading recording...");

        // Set upload status
        setStatus("uploading");

        // Get the MIME type from the blob
        const mimeType = recordingData.recordedBlob.type || "video/webm";

        // Create a File object from the Blob - exactly matching recording-options.tsx
        const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
        // Determine file extension based on mime type
        const fileExtension = mimeType.includes("mp4") ? "mp4" : "webm";

        // Create the file with the exact same format as recording-options.tsx
        const videoFile = new File(
          [recordingData.recordedBlob],
          `screen-recording-${timestamp}.${fileExtension}`,
          { type: mimeType }
        );

        // Import the API functions and handleVideoUploadWorkflow
        const { handleVideoUploadWorkflow } = await import(
          "~services/api-service"
        );

        // Upload the video using the same workflow function as recording-options.tsx
        try {
          // Call the same workflow function used in recording-options.tsx
          const result = await handleVideoUploadWorkflow(videoFile);

          console.log("Upload result:", result);

          if (result.success && result.processId) {
            console.log(
              "Video uploaded successfully with process ID:",
              result.processId
            );

            // Update status to uploaded
            setStatus("uploaded");

            // First open the dashboard
            chrome.runtime.sendMessage(
              {
                action: "openTalknician",
              },
              () => {
                // Send VIDEO_PROCESSING_STARTED event
                chrome.runtime.sendMessage({
                  action: "VIDEO_PROCESSING_STARTED",
                  process_id: result.processId,
                  message: result.message || "Video processing started",
                });

                // Then close this tab after a short delay
                setTimeout(() => {
                  window.close();
                }, 500);
              }
            );
          } else {
            console.error("Upload failed:", result.message);
            setError(
              `Failed to upload recording: ${result.message || "Upload failed"}`
            );
            setStatus("error");
          }
        } catch (uploadError) {
          console.error("Error uploading video:", uploadError);
          setError(
            `Failed to upload recording: ${uploadError.message || "Upload failed"}`
          );
          setStatus("error");
        }
      } catch (error) {
        console.error("Error in upload process:", error);
        setError(
          `Failed to upload recording: ${error.message || "Unknown error"}`
        );
        setStatus("error");
      }
    } else {
      setError("No recording available to upload");
    }
  };

  // Function to handle canceling the recording
  const handleCancel = () => {
    console.log("Canceling recording...");
    chrome.runtime.sendMessage({ action: "cancelRecording" }, (response) => {
      console.log("Cancel response:", response);
      // Tab will be closed by the background script
    });
  };

  // Note: handleNewRecording function was removed as it was unused

  return (
    <div className="font-sans p-5 max-w-full mx-auto bg-[#0F1116] text-white min-h-screen">
      <div className="flex justify-between items-center mb-5">
        <div className="flex items-center">
          <div className="ml-4 px-3 py-1 rounded-md bg-[#1A1D24] text-[#00E5C7] flex gap-2 items-center">
            <LogoIcon />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-base font-medium">
            Status:{" "}
            <span
              className={`
                ${status === "idle" ? "text-gray-400" : ""}
                ${status === "starting" ? "text-amber-400" : ""}
                ${status === "recording" ? "text-[#00E5C7] animate-pulse" : ""}
                ${status === "paused" ? "text-amber-400" : ""}
                ${status === "stopped" ? "text-gray-400" : ""}
                ${status === "uploading" ? "text-[#00E5C7] animate-pulse" : ""}
                ${status === "uploaded" ? "text-[#00E5C7]" : ""}
                ${status === "error" ? "text-red-500" : ""}
              `}
            >
              {status}
            </span>
          </div>
          <div className="text-base font-medium flex items-center">
            Timer:{" "}
            <span className="text-[#00E5C7] timer-display mx-1">
              {recordingState.recordingTime || "00:00"}
            </span>
            <button
              onClick={handleCancel}
              className="ml-2 text-red-400 hover:text-red-300 transition-colors w-6 h-6 flex items-center justify-center rounded-full border border-red-800 bg-[#1A1D24] hover:bg-[#252830]"
              title="Cancel Recording"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-[#2D1B1B] border border-red-800 text-red-400 px-4 py-3 rounded-md mb-5">
          <p>{error}</p>
        </div>
      )}

      <div className="w-full h-[400px] bg-[#1A1D24] rounded-lg overflow-hidden flex justify-center items-center mb-5 border border-[#252830]">
        {status === "recording" || status === "paused" ? (
          <video
            ref={videoRef}
            autoPlay
            muted
            className="w-[57%] h-full object-contain bg-black"
          />
        ) : status === "uploaded" ? (
          <div className="text-center text-[#00E5C7] p-5 flex flex-col items-center justify-center h-full">
            <svg
              className="w-16 h-16 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
            <p className="text-xl font-semibold">Upload Successful!</p>
            <p className="mt-2 text-gray-300">
              Your recording has been uploaded successfully.
            </p>
            <p className="mt-1 text-gray-400">Redirecting to dashboard...</p>
          </div>
        ) : status === "uploading" ? (
          <div className="text-center text-[#00E5C7] p-5 flex flex-col items-center justify-center h-full">
            <svg
              className="animate-spin w-16 h-16 mb-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <p className="text-xl font-semibold">Uploading...</p>
            <p className="mt-2 text-gray-300">
              Please wait while your recording is being uploaded.
            </p>
          </div>
        ) : status === "stopped" && recordingData.recordedUrl ? (
          //;
          <video
            src={recordingData.recordedUrl}
            controls
            autoPlay
            className="w-[57%] h-full object-contain bg-black"
          />
        ) : (
          <div className="text-center text-gray-400 p-5">
            <p>
              {status === "idle"
                ? "Ready to record. Click the start button in the recording controls to begin."
                : status === "starting"
                  ? "Starting recording..."
                  : status === "error"
                    ? "An error occurred with the recording."
                    : "No recording available."}
            </p>
          </div>
        )}
      </div>

      {/* Action buttons */}
      {(status === "stopped" ||
        status === "uploading" ||
        status === "uploaded") &&
        recordingData.recordedUrl && (
          <div className="flex justify-center gap-4 mt-5">
            {status === "uploading" ? (
              <button
                disabled
                className="bg-[#1A1D24] text-[#00E5C7] font-medium py-2 px-6 rounded-md flex items-center border border-[#00E5C7]"
              >
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-[#00E5C7]"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Uploading...
              </button>
            ) : status === "uploaded" ? (
              <button
                disabled
                className="bg-[#1A1D24] text-[#00E5C7] font-medium py-2 px-6 rounded-md flex items-center border border-[#00E5C7]"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  ></path>
                </svg>
                Upload Complete - Redirecting to Dashboard...
              </button>
            ) : (
              <button
                onClick={handleUpload}
                className="bg-[#1A1D24] hover:bg-[#252830] text-[#00E5C7] font-medium py-2 px-6 rounded-md transition-colors border border-[#00E5C7]"
              >
                Create Guide
              </button>
            )}

            {status !== "uploading" && status !== "uploaded" && (
              <>
                <button
                  onClick={handleCancel}
                  className="bg-[#1A1D24] hover:bg-[#252830] text-red-400 font-medium py-2 px-6 rounded-md transition-colors border border-red-800"
                >
                  Cancel
                </button>
              </>
            )}
          </div>
        )}
    </div>
  );
}
