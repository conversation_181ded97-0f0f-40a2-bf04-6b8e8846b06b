import { uploadVideo } from "~background/api";

/**
 * Handles the entire workflow for uploading a video:
 * 1. Gets current user's email
 * 2. Ensures the user exists (creates if needed)
 * 3. Uploads the video
 * @param videoFile The video file to upload
 * @returns The process ID for tracking video processing
 */
export async function handleVideoUploadWorkflow(videoFile: File): Promise<{
  success: boolean;
  processId?: string;
  message?: string;
}> {
  try {
    // Step 4: Upload video
    const processId = await uploadVideo(videoFile);

    return {
      success: true,
      processId,
      message: "Video upload successful",
    };
  } catch (error) {
    console.error("Error in video upload workflow:", error);
    return {
      success: false,
      message: error.message || "Failed to upload video",
    };
  }
}
