export function formatStepDescription(
  action: string,
  element: string,
  value?: string
): string {
  const normalizeText = (text: string) => {
    return text.trim().replace(/\s+/g, " "); // Replace multiple spaces with single space
  };

  switch (action) {
    case "click":
      return value
        ? `Click on ${normalizeText(value)}`
        : `Click on ${element.toLowerCase()}`;
    case "input":
      return value
        ? `Type "${normalizeText(value)}"`
        : `Type in ${element.toLowerCase()}`;
    default:
      return `${action} on ${element.toLowerCase()}`;
  }
}
