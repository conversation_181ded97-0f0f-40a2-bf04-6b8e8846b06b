import type { Shape } from "../types";

function drawSelectionBorder(
  ctx: CanvasRenderingContext2D,
  shape: Shape,
  selectedShape: Shape | null
): void {
  if (shape === selectedShape) {
    ctx.save();
    ctx.setLineDash([5, 5]);
    ctx.strokeStyle = "#0095ff";
    ctx.lineWidth = 2;
    const handleSize = 8;

    // Draw handles as larger squares
    ctx.fillStyle = "#ffffff";
    ctx.strokeStyle = "#0095ff";
    ctx.lineWidth = 2;

    const drawHandle = (x: number, y: number) => {
      ctx.fillRect(
        x - handleSize / 2,
        y - handleSize / 2,
        handleSize,
        handleSize
      );
      ctx.strokeRect(
        x - handleSize / 2,
        y - handleSize / 2,
        handleSize,
        handleSize
      );
    };

    if (shape.type === "arrow" || shape.type === "line") {
      // Draw selection box
      const padding = 5;
      const minX = Math.min(shape.startX!, shape.endX!) - padding;
      const minY = Math.min(shape.startY!, shape.endY!) - padding;
      const maxX = Math.max(shape.startX!, shape.endX!) + padding;
      const maxY = Math.max(shape.startY!, shape.endY!) + padding;
      ctx.strokeRect(minX, minY, maxX - minX, maxY - minY);

      // Draw endpoint handles
      drawHandle(shape.startX!, shape.startY!);
      drawHandle(shape.endX!, shape.endY!);
    } else if (shape.type === "circle") {
      // For circles, draw handles at corners of bounding box
      drawHandle(shape.x, shape.y);
      drawHandle(shape.x + shape.width, shape.y);
      drawHandle(shape.x + shape.width, shape.y + shape.height);
      drawHandle(shape.x, shape.y + shape.height);
    } else {
      // For other shapes
      drawHandle(shape.x, shape.y);
      drawHandle(shape.x + shape.width, shape.y);
      drawHandle(shape.x + shape.width, shape.y + shape.height);
      drawHandle(shape.x, shape.y + shape.height);
      // Draw edge handles
      drawHandle(shape.x + shape.width / 2, shape.y);
      drawHandle(shape.x + shape.width, shape.y + shape.height / 2);
      drawHandle(shape.x + shape.width / 2, shape.y + shape.height);
      drawHandle(shape.x, shape.y + shape.height / 2);
    }

    ctx.restore();
  }
}

function drawHandle(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  position: string
): void {
  const handleSize = 8;
  ctx.fillStyle = "#ffffff";
  ctx.fillRect(x - handleSize / 2, y - handleSize / 2, handleSize, handleSize);
  ctx.strokeStyle = "#0095ff";
  ctx.strokeRect(
    x - handleSize / 2,
    y - handleSize / 2,
    handleSize,
    handleSize
  );
}

function drawCircle(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  color: string
): void {
  const centerX = x + width / 2;
  const centerY = y + height / 2;
  const radiusX = Math.abs(width / 2);
  const radiusY = Math.abs(height / 2);

  ctx.beginPath();
  ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.stroke();
}

function drawLine(
  ctx: CanvasRenderingContext2D,
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  color: string
): void {
  ctx.beginPath();
  ctx.moveTo(startX, startY);
  ctx.lineTo(endX, endY);
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.stroke();
}

function drawArrow(
  ctx: CanvasRenderingContext2D,
  fromX: number,
  fromY: number,
  toX: number,
  toY: number,
  color: string = "red"
): void {
  const headLength = 15;
  const angle = Math.atan2(toY - fromY, toX - fromX);

  // Draw the line
  drawLine(ctx, fromX, fromY, toX, toY, color);

  // Draw the arrow head
  ctx.beginPath();
  ctx.moveTo(toX, toY);
  ctx.lineTo(
    toX - headLength * Math.cos(angle - Math.PI / 6),
    toY - headLength * Math.sin(angle - Math.PI / 6)
  );
  ctx.lineTo(
    toX - headLength * Math.cos(angle + Math.PI / 6),
    toY - headLength * Math.sin(angle + Math.PI / 6)
  );
  ctx.closePath();
  ctx.fillStyle = color;
  ctx.fill();
}

function getCursorStyle(position: string): string {
  switch (position) {
    case "nw":
    case "se":
      return "nwse-resize";
    case "ne":
    case "sw":
      return "nesw-resize";
    case "n":
    case "s":
      return "ns-resize";
    case "e":
    case "w":
      return "ew-resize";
    case "start":
    case "end":
      return "crosshair";
    case "move":
      return "move";
    default:
      return "default";
  }
}

function isOverHandle(x: number, y: number, shape: Shape): string | null {
  const handleSize = 8;

  if (shape.type === "arrow" || shape.type === "line") {
    // Check endpoints first with larger hit area
    if (isOverPoint(x, y, shape.startX!, shape.startY!, handleSize * 1.5)) {
      return "start";
    }
    if (isOverPoint(x, y, shape.endX!, shape.endY!, handleSize * 1.5)) {
      return "end";
    }
  } else if (shape.type === "circle") {
    // Check corners with larger hit area for circles
    const handleSize = 12; // Larger hit area for circle handles
    if (isOverPoint(x, y, shape.x, shape.y, handleSize)) return "nw";
    if (isOverPoint(x, y, shape.x + shape.width, shape.y, handleSize))
      return "ne";
    if (
      isOverPoint(
        x,
        y,
        shape.x + shape.width,
        shape.y + shape.height,
        handleSize
      )
    )
      return "se";
    if (isOverPoint(x, y, shape.x, shape.y + shape.height, handleSize))
      return "sw";
  } else {
    // Rectangle and other shapes
    if (isOverPoint(x, y, shape.x, shape.y, handleSize)) return "nw";
    if (isOverPoint(x, y, shape.x + shape.width, shape.y, handleSize))
      return "ne";
    if (
      isOverPoint(
        x,
        y,
        shape.x + shape.width,
        shape.y + shape.height,
        handleSize
      )
    )
      return "se";
    if (isOverPoint(x, y, shape.x, shape.y + shape.height, handleSize))
      return "sw";
    // Edge handles
    if (isOverPoint(x, y, shape.x + shape.width / 2, shape.y, handleSize))
      return "n";
    if (
      isOverPoint(
        x,
        y,
        shape.x + shape.width,
        shape.y + shape.height / 2,
        handleSize
      )
    )
      return "e";
    if (
      isOverPoint(
        x,
        y,
        shape.x + shape.width / 2,
        shape.y + shape.height,
        handleSize
      )
    )
      return "s";
    if (isOverPoint(x, y, shape.x, shape.y + shape.height / 2, handleSize))
      return "w";
  }
  return null;
}

function isOverPoint(
  x: number,
  y: number,
  px: number,
  py: number,
  size: number = 8
): boolean {
  return x >= px - size && x <= px + size && y >= py - size && y <= py + size;
}

function isOverShape(x: number, y: number, shape: Shape): boolean {
  const padding = 2;

  if (shape.type === "arrow" || shape.type === "line") {
    const minX = Math.min(shape.startX!, shape.endX!) - padding;
    const minY = Math.min(shape.startY!, shape.endY!) - padding;
    const maxX = Math.max(shape.startX!, shape.endX!) + padding;
    const maxY = Math.max(shape.startY!, shape.endY!) + padding;

    if (x >= minX && x <= maxX && y >= minY && y <= maxY) {
      const distToStart = Math.sqrt(
        (x - shape.startX!) ** 2 + (y - shape.startY!) ** 2
      );
      const distToEnd = Math.sqrt(
        (x - shape.endX!) ** 2 + (y - shape.endY!) ** 2
      );
      const lineLength = Math.sqrt(
        (shape.endX! - shape.startX!) ** 2 + (shape.endY! - shape.startY!) ** 2
      );
      return Math.abs(distToStart + distToEnd - lineLength) < 10;
    }
    return false;
  } else if (shape.type === "circle") {
    const centerX = shape.x + shape.width / 2;
    const centerY = shape.y + shape.height / 2;
    const radiusX = shape.width / 2 + padding;
    const radiusY = shape.height / 2 + padding;

    const normalizedX = (x - centerX) / radiusX;
    const normalizedY = (y - centerY) / radiusY;
    return normalizedX * normalizedX + normalizedY * normalizedY <= 1.1;
  } else {
    return (
      x >= shape.x - padding &&
      x <= shape.x + shape.width + padding &&
      y >= shape.y - padding &&
      y <= shape.y + shape.height + padding
    );
  }
}

function resizeShape(shape: Shape, x: number, y: number, handle: string): void {
  if (shape.type === "arrow" || shape.type === "line") {
    // Directly update endpoints for arrows/lines
    if (handle === "start") {
      shape.startX = x;
      shape.startY = y;
    } else if (handle === "end") {
      shape.endX = x;
      shape.endY = y;
    }
  } else if (
    shape.type === "circle" ||
    shape.type === "rectangle" ||
    shape.type === "blur"
  ) {
    let newX = shape.x;
    let newY = shape.y;
    let newWidth = shape.width;
    let newHeight = shape.height;

    switch (handle) {
      case "nw":
        newWidth = shape.x + shape.width - x;
        newHeight = shape.y + shape.height - y;
        newX = x;
        newY = y;
        break;
      case "ne":
        newWidth = x - shape.x;
        newHeight = shape.y + shape.height - y;
        newY = y;
        break;
      case "se":
        newWidth = x - shape.x;
        newHeight = y - shape.y;
        break;
      case "sw":
        newWidth = shape.x + shape.width - x;
        newHeight = y - shape.y;
        newX = x;
        break;
    }

    // Ensure width and height are positive
    if (newWidth < 0) {
      newX = newX + newWidth;
      newWidth = Math.abs(newWidth);
    }
    if (newHeight < 0) {
      newY = newY + newHeight;
      newHeight = Math.abs(newHeight);
    }

    shape.x = newX;
    shape.y = newY;
    shape.width = newWidth;
    shape.height = newHeight;
  }
}

function drawBlur(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number
): void {
  // Save the current canvas state
  ctx.save();

  // Create a temporary canvas to apply the blur effect
  const tempCanvas = document.createElement("canvas");
  const tempCtx = tempCanvas.getContext("2d");
  if (!tempCtx || width <= 0 || height <= 0) return;

  tempCanvas.width = width;
  tempCanvas.height = height;

  // Copy the region to be blurred to the temporary canvas
  tempCtx.drawImage(ctx.canvas, x, y, width, height, 0, 0, width, height);

  // Apply the blur filter to the temporary canvas
  tempCtx.filter = "blur(10px)";
  tempCtx.drawImage(tempCanvas, 0, 0);

  // Draw the blurred image back onto the main canvas
  ctx.drawImage(tempCanvas, x, y);

  // Restore the canvas state
  ctx.restore();
}

export {
  drawSelectionBorder,
  getCursorStyle,
  isOverHandle,
  isOverShape,
  resizeShape,
  drawArrow,
  drawCircle,
  drawLine,
  drawBlur,
};
