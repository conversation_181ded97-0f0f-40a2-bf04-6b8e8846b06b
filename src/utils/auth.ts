import { Storage } from "@plasmohq/storage";
import { fetchIsValidToken } from "~background/api";
import { getWebsiteUrl } from "~utils/domain-config";

const storage = new Storage();
const AUTH_TOKEN_KEY = "bearerToken";
let FE_URL: string;

const initializeFrontendUrl = async () => {
  FE_URL = await getWebsiteUrl();
};

// Initialize on module load
initializeFrontendUrl();

export async function getAuthToken(): Promise<string> {
  const token = await storage.get("bearerToken");
  if (!token) {
    const fallbackToken = process.env.PLASMO_PUBLIC_API_TOKEN;
    return fallbackToken;
  }
  return token as string;
}

export async function setAuthToken(token: string): Promise<void> {
  await storage.set(AUTH_TOKEN_KEY, token);
}

export async function removeAuthToken(): Promise<void> {
  await storage.remove(AUTH_TOKEN_KEY);
}

export async function isValidToken(token: string): Promise<boolean> {
  try {
    const isValid = await fetchIsValidToken();
    if (!isValid) {
      //clear token
      await removeAuthToken();
      redirectToLogin();
      return false;
    }
    return isValid;
  } catch (error) {
    console.error("Error validating token:", error);
    return false;
  }
}

export function redirectToLogin() {
  try {
    chrome.tabs.query({ url: `${FE_URL}/*` }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError);
        // Fallback: just create a new tab
        chrome.tabs.create({
          url: `${FE_URL}/login`,
          active: true,
        });
        return;
      }

      if (tabs && tabs.length > 0) {
        // Tab exists, activate it
        chrome.tabs.update(tabs[0].id, { active: true }, () => {
          if (chrome.runtime.lastError) {
            console.error(chrome.runtime.lastError);
            return;
          }
          chrome.windows.update(tabs[0].windowId, { focused: true });
        });
      } else {
        // Create new tab and activate it
        chrome.tabs.create({
          url: `${FE_URL}/login`,
          active: true,
        });
      }
    });
  } catch (error) {
    console.error("Error handling tab operation:", error);
    // Fallback: create new tab
    chrome.tabs.create({
      url: `${FE_URL}/login`,
      active: true,
    });
  }
}

// New helper function for auth checks
export async function checkAuthentication(): Promise<boolean> {
  const token = await getAuthToken();
  if (!token) {
    chrome.runtime.sendMessage({ action: "toggleToolbar", show: false });
    // Also hide the recording controls when hiding the toolbar due to auth failure
    chrome.runtime.sendMessage({ action: "hideUI" });
    redirectToLogin();
    return false;
  }
  return true;
}
