// Utility to load content scripts dynamically

/**
 * Loads a content script into the current page if it's not already loaded
 * @param scriptPath Path to the script to load
 * @param tabId Optional tab ID to load the script into
 */
export const loadContentScript = async (scriptPath: string, tabId?: number) => {
  // If no tabId is provided, get the active tab
  if (!tabId) {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length === 0) return;
    tabId = tabs[0].id;
  }

  try {
    // Check if the script is already loaded (using a messaging approach)
    const isLoaded = await chrome.tabs
      .sendMessage(tabId, {
        action: "CHECK_SCRIPT_LOADED",
        scriptPath,
      })
      .catch(() => false);

    if (isLoaded) return; // Script is already loaded

    // Inject the script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: [scriptPath],
    });

    console.log(`Content script ${scriptPath} loaded successfully`);
  } catch (error) {
    console.error(`Error loading content script ${scriptPath}:`, error);
  }
};

/**
 * Ensures the pin marker content script is loaded
 */
export const ensurePinMarkerScriptLoaded = async (tabId?: number) => {
  await loadContentScript("src/contents/pin-content.ts", tabId);
};
