import { Storage } from "@plasmohq/storage"

interface DomainConfig {
  WEBSITE_URL: string
  API_URL: string
}

const DOMAIN_CONFIG_KEY = "domain_config"
const LAST_FETCH_KEY = "domain_config_last_fetch"
const GITHUB_DOMAIN_URL = "https://raw.githubusercontent.com/talknician/talknician-public-extension-domain/refs/heads/main/domain.json"

const storage = new Storage()

export const getDomainConfig = async (): Promise<DomainConfig> => {
  const cachedConfig = await storage.get<DomainConfig>(DOMAIN_CONFIG_KEY)
  const lastFetch = await storage.get<number>(LAST_FETCH_KEY)
  const now = Date.now()

  // If we have cached config and it's less than 24 hours old, use it
  if (cachedConfig && lastFetch && now - lastFetch < 24 * 60 * 60 * 1000) {
    return cachedConfig
  }

  try {
    const response = await fetch(GITHUB_DOMAIN_URL)
    if (!response.ok) {
      throw new Error("Failed to fetch domain config")
    }
    
    const config = await response.json()
    await storage.set(DOMAIN_CONFIG_KEY, config)
    await storage.set(LAST_FETCH_KEY, now)
    return {
      WEBSITE_URL: config.WEBSITE_URL,
      API_URL: config.API_URL
    }
  } catch (error) {
    console.error("Error fetching domain config:", error)
    // Fallback to environment variables if fetch fails
    return {
      WEBSITE_URL: process.env.PLASMO_PUBLIC_FE_URL,
      API_URL: process.env.PLASMO_PUBLIC_API_URL
    }
  }
}

export const getApiUrl = async (): Promise<string> => {
  const config = await getDomainConfig()
  return process.env.NODE_ENV === "development" ?  process.env.PLASMO_PUBLIC_API_URL : config.API_URL
}

export const getWebsiteUrl = async (): Promise<string> => {
  const config = await getDomainConfig()
  return process.env.NODE_ENV === "development" ? "http://localhost:3000" : config.WEBSITE_URL
} 
