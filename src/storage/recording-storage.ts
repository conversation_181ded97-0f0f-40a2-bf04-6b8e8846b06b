import { Storage } from "@plasmohq/storage";

// Define the recording state interface
export interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  recordingTime: string;
  micAudioEnabled: boolean;
  systemAudioEnabled: boolean;
  captureType: "tab" | "screen";
  isUIVisible: boolean;
}

// Default recording state
export const defaultRecordingState: RecordingState = {
  isRecording: false,
  isPaused: false,
  recordingTime: "00:00",
  micAudioEnabled: true,
  systemAudioEnabled: true,
  captureType: "screen",
  isUIVisible: false,
};

// Create a storage instance
export const recordingStorage = new Storage({
  area: "local", // Use local storage area for better performance
  // These keys will be copied to localStorage for access in content scripts
  copiedKeyList: ["recording-state", "recording-settings"],
});

// Helper functions to get and set recording state
export async function getRecordingState(): Promise<RecordingState> {
  const state = await recordingStorage.get("recording-state");
  return state
    ? (state as unknown as RecordingState)
    : { ...defaultRecordingState };
}

export async function setRecordingState(
  state: Partial<RecordingState>
): Promise<void> {
  const currentState = await getRecordingState();
  await recordingStorage.set("recording-state", {
    ...currentState,
    ...state,
  });
}

// Helper functions for specific recording actions
export async function startRecording(): Promise<void> {
  await setRecordingState({
    isRecording: true,
    isPaused: false,
    recordingTime: "00:00",
    micAudioEnabled: true, // Always ensure mic is enabled
    systemAudioEnabled: true, // Always ensure system audio is enabled
  });
}

export async function pauseRecording(): Promise<void> {
  await setRecordingState({
    isPaused: true,
  });
}

export async function resumeRecording(): Promise<void> {
  await setRecordingState({
    isPaused: false,
  });
}

export async function stopRecording(): Promise<void> {
  await setRecordingState({
    isRecording: false,
    isPaused: false,
  });
}

export async function updateRecordingTime(time: string): Promise<void> {
  await setRecordingState({
    recordingTime: time,
  });
}

// These functions are kept for backward compatibility but always return true
export async function toggleMicAudio(): Promise<boolean> {
  // Always ensure mic is enabled
  await setRecordingState({
    micAudioEnabled: true,
  });
  return true;
}

export async function toggleSystemAudio(): Promise<boolean> {
  // Always ensure system audio is enabled
  await setRecordingState({
    systemAudioEnabled: true,
  });
  return true;
}

export async function setCaptureType(type: "tab" | "screen"): Promise<void> {
  await setRecordingState({
    captureType: type,
  });
}

// Function to toggle UI visibility
export async function toggleUIVisibility(): Promise<boolean> {
  const state = await getRecordingState();
  const newValue = !state.isUIVisible;
  await setRecordingState({
    isUIVisible: newValue,
    isRecording: false,
  });
  return newValue;
}

// Function to show UI
export async function showUI(): Promise<void> {
  await setRecordingState({
    isUIVisible: true,
  });
}

// Function to hide UI
export async function hideUI(): Promise<void> {
  await setRecordingState({
    isUIVisible: false,
  });
}

// Function to watch for changes to recording state
export function watchRecordingState(
  callback: (state: RecordingState) => void
): any {
  const unwatch = recordingStorage.watch({
    "recording-state": (change) => {
      if (change.newValue) {
        callback(change.newValue as unknown as RecordingState);
      }
    },
  });

  return unwatch;
}
