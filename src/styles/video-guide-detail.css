.video-guide-detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  color: #333;
  background: #fff;
}

.video-guide-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.open-browser-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.open-browser-button:hover {
  background: rgba(59, 130, 246, 0.15);
  color: #2563eb;
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.open-browser-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.open-browser-button svg {
  color: #3b82f6;
  width: 14px;
  height: 14px;
}

.open-browser-button:hover svg {
  color: #2563eb;
}

.open-browser-button span {
  font-weight: 500;
  font-size: 11px;
}

.back-button {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.2s;
  text-decoration: none;
}

.back-button:hover {
  color: #333;
}

.video-guide-title {
  font-size: 24px;
  font-weight: 600;
  margin: 16px 0 8px 0;
  color: #1a1a1a;
  line-height: 1.3;
}

.video-guide-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.video-steps-container {
  margin-bottom: 32px;
}

.video-steps-container h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1a1a1a;
}

.video-steps-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.video-step {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s;
}

.video-step:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.step-content {
  padding: 16px;
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 12px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  font-size: 14px;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 4px 0 12px 0;
}

.step-description {
  font-size: 14px;
  line-height: 1.6;
  color: #4b5563;
  margin-bottom: 12px;
}

.step-time {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.step-video-container {
  position: relative;
  margin: 0 -16px;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  min-height: 200px;
}

.step-video {
  width: 100%;
  display: block;
  background: #000;
  border-radius: 8px;
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Tab switching UI */
.step-content-tabs {
  margin-top: 16px;
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.step-tabs-header {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.step-tab-button {
  padding: 8px 16px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  flex: 1;
}

.step-tab-button:hover {
  color: #3b82f6;
}

.step-tab-button.active {
  color: #3b82f6;
}

.step-tab-button.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
}

.step-tab-content {
  padding: 8px 0;
}

.step-summary {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
  line-height: 1.5;
}

.step-transcription {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.6;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f9fafb;
  border-radius: 6px;
}

.loading-text,
.error-text {
  font-size: 14px;
  color: #6b7280;
  padding: 12px;
  text-align: center;
}

.error-text {
  color: #ef4444;
}

.video-guide-actions {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.action-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background: #2563eb;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  padding: 16px;
  border-radius: 8px;
  margin-top: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.error-message h2 {
  font-size: 20px;
  color: #dc2626;
  margin-bottom: 8px;
}

.error-message p {
  color: rgb(239, 68, 68);
  font-size: 14px;
  margin: 0;
}

.retry-button {
  background: rgb(239, 68, 68);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: rgb(220, 38, 38);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .video-guide-detail-container {
    padding: 12px;
  }

  .video-guide-title {
    font-size: 20px;
  }

  .video-step {
    border-radius: 8px;
  }

  .step-content {
    padding: 12px;
  }

  .step-video-container {
    margin: 0 -12px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .step-title {
    font-size: 16px;
  }

  .action-button {
    width: 100%;
  }
}

/* Video controls customization */
.step-video::-webkit-media-controls {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.step-video::-webkit-media-controls-panel {
  display: flex !important;
  opacity: 1 !important;
}

.loading-message {
  text-align: center;
  padding: 32px;
  color: #6b7280;
  font-size: 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-message::after {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Loading indicators */
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 8px;
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.loading-spinner-large {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Remove the custom progress bar styles we previously added */
.custom-progress-container,
.custom-progress-bar,
.play-button {
  display: none;
}

/* Modern Video Controls */
.video-controls {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(23, 25, 35, 0.85);
  border-radius: 8px;
  margin-top: 4px;
  gap: 16px;
  position: relative;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.play-pause-button,
.volume-button,
.fullscreen-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
  padding: 0;
}

.play-pause-button:hover,
.volume-button:hover,
.fullscreen-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.play-pause-button:active,
.volume-button:active,
.fullscreen-button:active {
  transform: scale(0.95);
}

.fullscreen-button img {
  width: 18px;
  height: 18px;
  filter: invert(1);
}

.time-display {
  color: white;
  font-size: 13px;
  font-weight: 500;
  min-width: 42px;
  text-align: center;
  user-select: none;
}

.progress-track {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.progress-bar {
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  border-radius: 3px;
  transition: width 0.1s;
}

.progress-track:hover {
  height: 8px;
  margin: -1px 0;
}

.progress-track:hover .progress-bar {
  background: linear-gradient(90deg, #60a5fa, #3b82f6);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-slider {
  width: 60px;
  height: 4px;
  -webkit-appearance: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  transition: height 0.2s;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  transition: transform 0.1s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  transition: transform 0.1s;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.volume-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.volume-slider:focus {
  outline: none;
  height: 6px;
}

/* Fullscreen mode styles */
.step-video-container:-webkit-full-screen,
.step-video-container:-moz-full-screen,
.step-video-container:-ms-fullscreen,
.step-video-container:fullscreen,
.step-video-container.custom-fullscreen {
  width: 100vw;
  height: 100vh;
  background: black;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.step-video-container:-webkit-full-screen .step-video,
.step-video-container:-moz-full-screen .step-video,
.step-video-container:-ms-fullscreen .step-video,
.step-video-container:fullscreen .step-video,
.step-video-container.custom-fullscreen .step-video {
  max-height: calc(100vh - 80px);
  width: auto;
  max-width: 100%;
  margin: 0 auto;
  object-fit: contain;
}

.step-video-container:-webkit-full-screen .video-controls,
.step-video-container:-moz-full-screen .video-controls,
.step-video-container:-ms-fullscreen .video-controls,
.step-video-container:fullscreen .video-controls,
.step-video-container.custom-fullscreen .video-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  width: calc(100% - 40px);
  border-radius: 12px;
  background: rgba(23, 25, 35, 0.6);
  backdrop-filter: blur(12px);
  z-index: 10000;
}

/* Hide native video controls */
.step-video::-webkit-media-controls-enclosure,
.step-video::-webkit-media-controls-timeline,
.step-video::-webkit-media-controls-current-time-display,
.step-video::-webkit-media-controls-time-remaining-display,
.step-video::-webkit-media-controls-volume-slider,
.step-video::-webkit-media-controls-mute-button {
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Ensure video has no default controls */
.step-video {
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Custom fullscreen fallback */
.custom-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: black !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.custom-fullscreen .step-video {
  max-height: calc(100vh - 80px);
  max-width: 100vw;
  width: auto;
  height: auto;
  object-fit: contain;
}

.custom-fullscreen .video-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  width: calc(100% - 40px);
}

.custom-fullscreen-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  z-index: 10000;
}

.custom-fullscreen-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* Fix for various browser issues */
.step-video-container:-webkit-full-screen,
.step-video-container:-moz-full-screen,
.step-video-container:-ms-fullscreen,
.step-video-container:fullscreen {
  position: fixed !important;
  width: 100vw !important;
  height: 100vh !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: auto !important;
  background: #000 !important;
  z-index: 9999 !important;
}

/* Additional debugging css */
.fullscreen-debug {
  position: fixed;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  font-size: 12px;
  z-index: 10001;
  border-radius: 4px;
  white-space: pre-wrap;
  max-width: 80vw;
  max-height: 30vh;
  overflow: auto;
}
