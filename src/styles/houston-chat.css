.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  transition: all 0.2s ease;
  z-index: 1;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-button::before,
.close-button::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 2px;
  background-color: currentColor;
  transform-origin: center;
}

.close-button::before {
  transform: rotate(45deg);
}

.close-button::after {
  transform: rotate(-45deg);
}

.sidebar {
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  overflow-y: auto;
}

.sidebar.right-sidebar {
  border-right: none;
  border-left: 1px solid rgba(255, 255, 255, 0.05);
}

.chat-container {
  all: initial;
  position: fixed;
  inset: 50% auto auto 50%;
  transform: translate(-50%, -50%);
  z-index: 2147483646;
  display: grid;
  grid-template-columns: 280px minmax(500px, 1fr) 280px;
  border-radius: 24px;
  width: 90vw;
  max-width: 1536px;
  height: 80vh;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  background-color: rgb(18, 18, 18);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 1rem;
}

.new-chat-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
  background-color: rgba(255, 255, 255, 0.1);
}

.new-chat-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.new-chat-button svg {
  width: 20px;
  height: 20px;
}

.sidebar-section {
  margin-top: 0.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.chat-section-header {
  color: hsl(240, 5%, 75%);
  font-size: 1rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.history-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.history-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 4px;
  margin: 4px 0;
}

.history-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.history-item.active {
  background-color: rgba(0, 122, 255, 0.1);
}

.history-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item-time {
  font-size: 14px;
  color: #6B7280;
}

.main-content {
  padding: 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  margin-bottom: 1rem;
}

.message.user {
  background-color: rgba(66, 133, 244, 0.15);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  color: #fff;
  font-size: 1rem;
  line-height: 1.5;
}

.message.assistant {
  margin-bottom: 2rem;
}

.message-content {
  background-color: rgba(255, 255, 255, 0.08);
  padding: 1.5rem;
  border-radius: 12px;
  color: #fff;
  font-size: 1rem;
  line-height: 1.5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-paragraph {
  min-height: 1.5rem;
}
p.message-content {
  margin: 0;
}

.message-paragraph:first-child {
  padding-top: 0;
}

.message-paragraph:last-child {
  padding-bottom: 0;
}

.message-heading-1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.message-heading-2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  color: #fff;
}

.message-heading-3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem;
  color: #4285f4;
}

.message-list {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.message-ordered-list {
  margin: 1rem 0;
  padding-left: 1.5rem;
  list-style-type: decimal;
}

.message-list-item {
  margin: 0.5rem 0;
  padding-left: 0.5rem;
}

.message-list-item::marker {
  color: #4285f4;
}

.message-content h1,
.message-content h2,
.message-content h3 {
  color: #fff;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.message-content h1 {
  font-size: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.message-content h2 {
  font-size: 1.25rem;
}

.message-content h3 {
  font-size: 1.1rem;
  color: #4285f4;
}

.message-content p {
  margin: 0;
}

.message-content ul,
.message-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.message-content li {
  margin: 0.5rem 0;
}

.message-content strong {
  color: #4285f4;
  font-weight: 600;
}

.message-content code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9em;
}

.message-content blockquote {
  border-left: 3px solid #4285f4;
  margin: 1rem 0;
  padding: 0.5rem 0 0.5rem 1rem;
  background: rgba(66, 133, 244, 0.1);
  border-radius: 4px;
}

.message-content hr {
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin: 1.5rem 0;
}

.suggested-questions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem 0;
}

.suggested-question {
  background-color: rgba(66, 133, 244, 0.1);
  border: none;
  border-radius: 100px;
  padding: 0.5rem 1rem;
  color: #4285f4;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggested-question:hover {
  background-color: rgba(66, 133, 244, 0.2);
}

.follow-up {
  margin-bottom: 2rem;
  position: relative;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(66, 133, 244, 0.5);
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.plus-icon {
  color: hsl(240, 5%, 66%);
  margin-right: 0.5rem;
  margin-left: 0.5rem;
}

.chat-input {
  width: 100%;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 1rem;
  padding: 0.5rem;
  outline: none;
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.loading {
  color: rgba(255, 255, 255, 0.5);
  margin-left: 0.5rem;
  font-size: 0.9rem;
}

.send-button {
  background: none;
  border: none;
  color: #4285f4;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.send-button:hover {
  opacity: 0.8;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.sources-list {
  list-style-type: none;
  padding-inline-start: 0;
}

.source-item {
  padding: 12px;
  margin: 8px 0;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  cursor: pointer;
  color: hsl(240, 5%, 66%);
  transition: background-color 0.2s ease;
}

.source-item:hover {
  background-color: rgba(255, 255, 255, 0.12);
}

.show-all-button {
  width: 100%;
  padding: 0.5rem;
  margin-top: 1rem;
  background: none;
  border: none;
  color: hsl(240, 5%, 66%);
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.show-all-button:hover {
  background-color: hsl(240, 5%, 16%);
}

@media (max-width: 1200px) {
  .chat-container {
    grid-template-columns: 240px 1fr 240px;
  }
}

@media (max-width: 992px) {
  .chat-container {
    grid-template-columns: 200px 1fr;
  }
  .right-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .chat-container {
    grid-template-columns: 1fr;
    height: 90vh;
    width: 95vw;
  }
  .sidebar {
    display: none;
  }
}

.plus-icon-container {
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.plus-icon-container:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.plus-icon {
  color: hsl(240, 5%, 66%);
  margin-right: 0.5rem;
  margin-left: 0.5rem;
  width: 20px;
  height: 20px;
}

.sources-container {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.source-item {
  padding: 12px;
  margin: 8px 0;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.source-item:hover {
  background-color: rgba(255, 255, 255, 0.12);
}

.source-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.source-title {
  color: #fff;
  font-size: 1rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-source {
  border-left: 3px solid #ff0000;
}

.suggested-questions {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 0;
  right: 0;
  background-color: rgba(18, 18, 18, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.suggested-question {
  text-align: left;
  background-color: rgba(66, 133, 244, 0.1);
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  color: #4285f4;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.suggested-question:hover {
  background-color: rgba(66, 133, 244, 0.2);
}

@keyframes loading-dots {
  0%,
  20% {
    content: ".";
  }
  40%,
  60% {
    content: "..";
  }
  80%,
  100% {
    content: "...";
  }
}

.loading-message {
  position: relative;
}

.loading-message::after {
  content: "";
  animation: loading-dots 1.5s infinite;
  position: absolute;
  display: inline-block;
}

.source-group {
  margin-bottom: 1.5rem;
}

.source-group-title {
  color: hsl(240, 5%, 75%);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  padding-left: 4px;
}

.show-more-button {
  width: 100%;
  padding: 8px;
  margin-top: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border: none;
  border-radius: 6px;
  color: #4285f4;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.show-more-button:hover {
  background-color: rgba(255, 255, 255, 0.08);
}
