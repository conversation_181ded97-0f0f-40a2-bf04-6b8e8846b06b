/* Pin <PERSON>ails Styles */
:root {
  --primary-color: #4a90e2;
  --secondary-color: #f5f5f5;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --success-color: #28a745;
  --error-color: #dc3545;
  --hover-color: #e9f2fe;
  --dev-color: #7b2cbf;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.pin-details-container {
  width: 100%;
  margin: 0 auto;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-top: 0;
  margin-bottom: 0;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 20px;
}

/* Header with title and timestamp */
.pin-header {
  margin-bottom: 16px;
  overflow-wrap: break-word;
}

.pin-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
  word-break: break-word;
  cursor: pointer;
}

.pin-timestamp-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pin-timestamp {
  color: #777;
  font-size: 14px;
}

.delete-pin-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-pin-button:hover {
  background-color: #c82333;
}

.edit-title-container {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.edit-title {
  width: 100%;
  font-size: 16px;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  resize: vertical;
  min-height: 60px;
}

.save-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  align-self: flex-start;
}

.save-button:hover {
  background-color: #3a7bc8;
}

/* Tabs for Guides and Knowledge */
.tabs-container {
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  width: 100%;
  height: 60vh;
  overflow: auto;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  width: 100%;
  flex-wrap: nowrap;
}

.tab-button {
  padding: 10px 15px;
  background: transparent;
  border: none;
  cursor: pointer;
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #555;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-button.active {
  color: var(--primary-color);
  font-weight: 500;
  background-color: #f8f9fa;
  border-bottom: 2px solid var(--primary-color);
}

.tab-content {
  padding: 15px;
  min-height: 60px;
  width: 100%;
}

/* Location section */
.pin-metadata {
  margin-bottom: 16px;
  width: 100%;
}

.metadata-section h3 {
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: 500;
}

.metadata-section .creator-info {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
  padding: 12px;
  background-color: #f9f9fb;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.metadata-section .creator-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.metadata-section .creator-row:last-child {
  margin-bottom: 0;
}

.metadata-section .creator-label {
  font-weight: 500;
  color: #777;
  font-size: 13px;
  width: 100px;
  flex-shrink: 0;
}

.metadata-section .creator-value {
  font-size: 13px;
  color: #777;
}

.location-url {
  display: block;
  margin-bottom: 5px;
  color: var(--primary-color);
  cursor: pointer;
  word-break: break-word;
}

.location-url:hover {
  text-decoration: underline;
}

.location-details {
  font-size: 14px;
  color: #666;
  overflow-wrap: break-word;
}

.location-details code {
  background-color: #f5f5f5;
  padding: 2px 5px;
  border-radius: 3px;
  font-family: monospace;
  display: inline-block;
  word-break: break-all;
  max-width: 100%;
  overflow-x: auto;
}

/* Additional Information Panel */
.additional-info {
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
  font-weight: 500;
}

.info-header-toggle {
  background: transparent;
  border: none;
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;
}

.info-content {
  padding: 15px;
}

.metadata-table {
  width: 100%;
}

.metadata-row {
  display: flex;
  margin-bottom: 8px;
}

.metadata-label {
  flex: 0 0 140px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.metadata-value {
  flex: 1;
  font-size: 14px;
  word-break: break-word;
}

/* Comments section */
.comments-section {
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
  font-weight: 500;
}

.comments-toggle {
  background: transparent;
  border: none;
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;
}

.comment-input {
  width: 100%;
  padding: 10px 15px;
  border: none;
  border-bottom: 1px solid var(--border-color);
  resize: vertical;
  min-height: 80px;
  font-size: 14px;
}

.comment-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
}

.comment-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 6px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.users-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #555;
}

/* Toggle switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Image styles */
.pin-image-container {
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.pin-image {
  width: 100%;
  height: auto;
  display: block;
  max-height: 400px;
  object-fit: contain;
}

.pin-image-caption {
  padding: 8px;
  background-color: #f8f9fa;
  color: #666;
  font-size: 12px;
  text-align: center;
  border-top: 1px solid var(--border-color);
}

/* Footer */
.footer {
  margin-top: 30px;
  text-align: center;
  color: #888;
  font-size: 12px;
}

.pin-content {
  display: flex;
  flex: 1;
}

.linked-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.linked-item:last-child {
  border-bottom: none;
}

.linked-item:hover {
  background-color: var(--hover-color);
}

.linked-item-thumbnail {
  width: 80px;
  height: 60px;
  margin-right: 12px;
  overflow: hidden;
  border-radius: 4px;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

.linked-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.linked-item-details {
  flex: 1;
  min-width: 0;
  margin-right: 40px; /* Make space for the link toggle */
}

.linked-item-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.linked-item-details p {
  margin: 0;
  font-size: 12px;
  color: #777;
  line-height: 1.4;
}

/* Section header with actions */
.section-header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 15px;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.selection-toggle-button {
  background-color: #f0f0f0;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selection-toggle-button.active {
  background-color: var(--primary-color);
  color: white;
}

.selection-toggle-button:hover {
  background-color: #e0e0e0;
}

.selection-toggle-button.active:hover {
  background-color: #3a7bc8;
}

.delete-selected-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-selected-button:hover {
  background-color: #c82333;
}

.delete-selected-button:disabled {
  background-color: #f8d7da;
  cursor: not-allowed;
}

/* Selection mode styles */
.linked-item.selection-mode {
  padding-left: 40px; /* Make space for the checkbox */
}

.linked-item.selection-mode.selected {
  background-color: #e9f2fe;
}

.selection-checkbox {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.selection-checkbox input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Link toggle styles */
.link-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
}

.link-toggle.linked {
  background-color: #28a745;
  color: white;
}

.link-toggle.unlinked {
  background-color: #e0e0e0;
  color: #555;
}

.link-toggle:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.link-icon {
  font-size: 16px;
  font-weight: bold;
}

.linked-icon {
  color: white;
}

.unlinked-icon {
  color: #555;
}

/* Status messages */
.loading-message,
.no-items-message {
  padding: 20px;
  text-align: center;
  color: #777;
  font-style: italic;
}

/* Knowledge tab styles */
.create-knowledge-section {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.knowledge-prompt {
  margin-bottom: 15px;
  font-size: 16px;
}

.create-knowledge-btn {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-knowledge-btn:hover {
  background-color: #3a7bc8;
}

.linked-items-header {
  font-size: 16px;
  font-weight: 500;
  margin: 16px 0 8px 0;
  color: var(--text-color);
}

.linked-items-list {
  list-style: none;
  padding: 0;
  margin: 0;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.linked-guide-item,
.linked-knowledge-item {
  background-color: rgba(59, 130, 246, 0.05);
  border-left: 3px solid rgba(59, 130, 246, 0.5);
}

/* Toast notification styles */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out forwards;
  max-width: 300px;
}

.toast-notification p {
  margin: 0;
}

.toast-notification.success {
  background-color: var(--success-color);
}

.toast-notification.error {
  background-color: var(--error-color);
}

.toast-notification.info {
  background-color: var(--primary-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
