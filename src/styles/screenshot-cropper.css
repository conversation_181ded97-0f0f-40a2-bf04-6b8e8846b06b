.screenshot-cropper {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  cursor: crosshair;
}

.cropper-container {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cropper-container canvas {
  max-width: 98vw;
  max-height: 90vh;
  object-fit: contain;
}

.cropper-controls {
  position: fixed;
  top: 20px;
  right: 20px;
}

.cropper-controls button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: #2c2c2e;
  color: white;
  cursor: pointer;
  font-size: 14px;
}

.cropper-controls button:hover {
  background-color: #3c3c3e;
}
