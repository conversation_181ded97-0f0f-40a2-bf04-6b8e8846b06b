
* {
  font-family: 'Geist<PERSON>ans', sans-serif;
}

/* Reset */
.knowledge-capture * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Modal overlay */
.knowledge-capture {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
}

/* Main container */
.knowledge-capture .capture-container {
  width: 90vw;
  height: 90vh;
  background-color: #1c1c1e;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24);
}

/* Header */
.knowledge-capture .capture-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 20px;
}

.knowledge-capture .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.knowledge-capture .header-icon {
  width: 24px;
  height: 24px;
  color: #5ee7df;
}

.knowledge-capture .header-title {
  color: #ffffff;
  font-size: 22px;
  font-weight: 500;
  padding: 0;
  margin: 0;
}

.knowledge-capture .close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.knowledge-capture .close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.knowledge-capture .close-button svg {
  width: 20px;
  height: 20px;
}

/* Toolbar */
.knowledge-capture .toolbar {
  position: relative;
  padding: 12px 0;
  display: flex;
  justify-content: center;
  background-color: #1c1c1e;
}

.knowledge-capture .toolbar-content {
  display: flex;
  background-color: #2c2c2e;
  border-radius: 8px;
  padding: 6px;
  gap: 6px;
}

.knowledge-capture .tool-group {
  display: flex;
  gap: 6px;
  padding: 0 6px;
}

.knowledge-capture .tool-button {
  background: none;
  border: none;
  color: #ffffff;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.knowledge-capture .tool-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.knowledge-capture .tool-button.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.knowledge-capture .tool-button svg {
  width: 20px;
  height: 20px;
}

/* Canvas area */
.knowledge-capture .canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #2c2c2e;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  padding: 20px;
}

canvas {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  display: block;
  object-fit: contain;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.knowledge-capture .text-edit-overlay {
  position: absolute;
  pointer-events: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.knowledge-capture .text-box {
  position: absolute;
  display: none;
  pointer-events: auto;
}

.knowledge-capture .text-input {
  position: absolute;
  background: transparent;
  border: none;
  outline: none;
  color: red;
  font-family: Arial;
  font-size: 16px;
  padding: 0;
  resize: none;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* Description area */
.knowledge-capture .description-container {
  background-color: #000000;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.knowledge-capture .description-label {
  color: #ffffff;
  font-weight: normal;
  padding-bottom: 8px;
  font-size: 16px;
}

.knowledge-capture .description-textarea {
  width: 100%;
  min-height: 100px;
  background-color: #1c1c1e;
  border: none;
  border-radius: 8px;
  padding: 16px;
  color: rgb(228, 228, 231);
  font-size: 16px;
  resize: none;
  outline: none;
}

.knowledge-capture .description-textarea::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-size: 16px;
}

/* Footer */
.knowledge-capture .capture-footer {
  padding: 8px 20px;
  display: flex;
  flex-direction: column;
  background-color: #000000;
  border-top: none;
}

.knowledge-capture .create-button-container {
  display: flex;
  color: #ffffff;
  align-items: center;
  justify-content: flex-end;
  padding: 16px 20px;
  gap: 8px;
}

.knowledge-capture .error-message {
  color: #ff4d4d;
  font-size: 14px;
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(255, 77, 77, 0.1);
  width: 100%;
  margin-bottom: 8px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.knowledge-capture .create-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 4px 24px -1px rgba(0, 0, 0, 0.25), 0 0 15px #40F7FF;
  backdrop-filter: blur(10px);
  color: #ffffff;
  font-size: 15px;
  font-weight: 500;
  padding: 10px 20px;
  background: linear-gradient(135deg,  rgba(0, 144, 163, 0.5) 13.25%, rgba(0, 144, 163, 0.15) 78.01%);
  border: 2px solid #40F7FF;
  border-radius: 8px;
  transition: 0.3s;
}


.knowledge-capture .create-button:hover:not(:disabled) {
  background-color: #0cd1d8;
}

.knowledge-capture .create-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Optional loading animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.knowledge-capture .create-button:disabled {
  animation: pulse 1.5s infinite;
}

/* ColorPicker Styles */
.knowledge-capture .color-picker {
  position: relative;
  display: flex;
  align-items: center;
}

.knowledge-capture .color-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  cursor: pointer;
  padding: 0;
  transition: transform 0.2s;
}

.knowledge-capture .color-circle:hover {
  transform: scale(1.1);
}

.knowledge-capture .color-picker-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  background-color: #2c2c2e;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.knowledge-capture .color-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
}

.knowledge-capture .color-option {
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.knowledge-capture .color-swatch {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  transition: transform 0.2s;
}

.knowledge-capture .color-option:hover .color-swatch {
  transform: scale(1.1);
}

.canvas-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-edit-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.text-box {
  position: absolute;
  display: none;
  pointer-events: auto;
}

.text-input {
  position: absolute;
  background: transparent;
  border: none;
  outline: none;
  color: inherit;
  font-family: Arial;
  font-size: 16px;
  padding: 0;
  resize: none;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.text-input:focus {
  outline: 2px solid #0095ff;
}

.text-shape-input {
  position: absolute;
  background: transparent;
  border: none;
  outline: none;
  font-family: Arial;
  font-size: 16px;
  padding: 0;
  margin: 0;
  pointer-events: auto;
}

.text-shape-input:focus {
  outline: 2px solid #0095ff;
}
