.guide-detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  color: #333;
}

.guide-header {
  margin-bottom: 32px;
  border-bottom: 1px solid #eee;
  padding-bottom: 24px;
}

.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.open-browser-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.open-browser-button:hover {
  background: rgba(59, 130, 246, 0.15);
  color: #2563eb;
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.open-browser-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.open-browser-button svg {
  color: #3b82f6;
  width: 14px;
  height: 14px;
}

.open-browser-button:hover svg {
  color: #2563eb;
}

.open-browser-button span {
  font-weight: 500;
  font-size: 11px;
}

.back-button {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.2s;
}

.back-button:hover {
  color: #333;
}

.guide-title {
  font-size: 28px;
  font-weight: 700;
  margin: 16px 0;
  color: #1a1a1a;
}

.guide-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.guide-content {
  margin-bottom: 32px;
}

.guide-thumbnail {
  margin-bottom: 32px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guide-thumbnail img {
  width: 100%;
  height: auto;
  display: block;
}

.guide-steps {
  margin-top: 32px;
}

.guide-steps h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1a1a1a;
}

.guide-step {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.guide-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 32px;
  height: 32px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content p {
  margin: 0 0 16px 0;
  line-height: 1.6;
  color: #444;
}

.step-screenshot {
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-screenshot img {
  width: 100%;
  height: auto;
  display: block;
}

.guide-actions {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.action-button {
  background: #007aff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background: #0056b3;
}

.error-message {
  text-align: center;
  padding: 48px 24px;
}

.error-message h2 {
  font-size: 24px;
  color: #dc3545;
  margin-bottom: 16px;
}

.error-message p {
  color: #666;
  font-size: 16px;
}
