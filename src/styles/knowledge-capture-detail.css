.knowledge-detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  color: #333;
}

.knowledge-header {
  margin-bottom: 32px;
  border-bottom: 1px solid #eee;
  padding-bottom: 24px;
}

.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.open-browser-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.open-browser-button:hover {
  background: rgba(59, 130, 246, 0.15);
  color: #2563eb;
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.open-browser-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.open-browser-button svg {
  color: #3b82f6;
  width: 14px;
  height: 14px;
}

.open-browser-button:hover svg {
  color: #2563eb;
}

.open-browser-button span {
  font-weight: 500;
  font-size: 11px;
}

.back-button {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.2s;
}

.back-button:hover {
  color: #333;
}

.knowledge-title {
  font-size: 24px;
  font-weight: 600;
  margin: 16px 0 8px 0;
  color: #1a1a1a;
  line-height: 1.3;
}

.knowledge-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.knowledge-content {
  margin-bottom: 32px;
}

.knowledge-image-container {
  position: relative;
  margin-bottom: 32px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #f5f5f5;
}

.knowledge-image-wrapper {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.knowledge-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.image-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.image-control-button {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0;
}

.image-control-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.image-control-button:active {
  transform: scale(0.95);
}

.image-control-button svg {
  width: 20px;
  height: 20px;
}

.knowledge-description {
  margin-top: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eee;
}

.knowledge-description h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.knowledge-description p {
  margin: 0;
  line-height: 1.6;
  color: #444;
  white-space: pre-wrap;
}

.knowledge-url {
  margin-top: 16px;
  padding: 12px 16px;
  background: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #e0edff;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #3b82f6;
  word-break: break-all;
}

.knowledge-url a {
  color: #3b82f6;
  text-decoration: none;
}

.knowledge-url a:hover {
  text-decoration: underline;
}

.knowledge-url svg {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.error-message {
  text-align: center;
  padding: 48px 24px;
}

.error-message h2 {
  font-size: 24px;
  color: #dc3545;
  margin-bottom: 16px;
}

.error-message p {
  color: #666;
  font-size: 16px;
}

.loading {
  text-align: center;
  padding: 48px 24px;
}

.loading p {
  color: #666;
  font-size: 16px;
}

/* Fullscreen mode */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.fullscreen .knowledge-image {
  max-height: 90vh;
  max-width: 90vw;
  width: auto;
  height: auto;
  object-fit: contain;
}

.fullscreen .image-controls {
  position: fixed;
  top: 20px;
  right: 20px;
}

.fullscreen .exit-fullscreen-button {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.fullscreen .exit-fullscreen-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .knowledge-detail-container {
    padding: 16px;
  }

  .knowledge-title {
    font-size: 20px;
  }

  .knowledge-meta {
    flex-direction: column;
    gap: 8px;
  }

  .image-controls {
    top: 8px;
    right: 8px;
  }

  .image-control-button {
    width: 32px;
    height: 32px;
  }

  .image-control-button svg {
    width: 16px;
    height: 16px;
  }
}

.metadata-section .creator-info {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
  padding: 12px;
  background-color: #f9f9fb;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.metadata-section .creator-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.metadata-section .creator-row:last-child {
  margin-bottom: 0;
}

.metadata-section .creator-label {
  font-weight: 500;
  color: #777;
  font-size: 13px;
  width: 100px;
  flex-shrink: 0;
}

.metadata-section .creator-value {
  font-size: 13px;
  color: #777;
}
