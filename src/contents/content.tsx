import React from "react";
import ReactDOM from "react-dom/client";
import { CaptureOverlay } from "~components/CaptureOverlay";
import { Storage } from "@plasmohq/storage";
import { CaptureStartOverlay } from "./start-capture-overlay";
import { formatStepDescription } from "~utils/format-step-description";
import type { Step } from "~background/guide-manager";
import { knowledgeManager } from "~background/knowledge-manager";
import { removeAuthToken, setAuthToken } from "~utils/auth";
import { getWebsiteUrl } from "~utils/domain-config";
import {
  getRecordingState,
  setRecordingState,
} from "~storage/recording-storage";

const storage = new Storage({
  area: "local",
});
let FE_URL: string;
let lastHighlightedElement: HTMLElement | null = null;
let isRecording = false; // Default value

const initializeFrontendUrl = async () => {
  FE_URL = await getWebsiteUrl();
};

// Initialize on module load
initializeFrontendUrl();

// Subscribe to changes in isRecording
storage.watch({
  "is-recording": (c) => {
    const newIsRecording = Boolean(c.newValue);
    // Show overlay when recording starts
    if (newIsRecording && !isRecording) {
      showCaptureOverlay();
      chrome.runtime.sendMessage({ action: "toggleToolbar", show: false });
    }
    isRecording = newIsRecording;
  },
});

// Initialize the value
storage.get("is-recording").then((value) => {
  isRecording = Boolean(value);
});

function showCaptureOverlay() {
  const overlayContainer = document.createElement("div");
  document.body.appendChild(overlayContainer);

  const root = ReactDOM.createRoot(overlayContainer);
  root.render(
    <CaptureStartOverlay
      onAnimationComplete={() => {
        root.unmount();
        document.body.removeChild(overlayContainer);
      }}
    />
  );
}

chrome.runtime.onMessage.addListener(
  async (
    request: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ) => {
    if (request.action === "startCapture") {
      const overlayContainer = document.createElement("div");
      document.body.appendChild(overlayContainer);

      // Store the pinId if it exists in the request
      const pinId = request.pinId;

      const root = ReactDOM.createRoot(overlayContainer);
      root.render(
        <CaptureOverlay
          onCapture={(bounds) => {
            chrome.runtime.sendMessage({
              action: "performCapture",
              bounds,
              pinId, // Pass the pinId to the performCapture action
            });
            root.unmount();
            document.body.removeChild(overlayContainer);
          }}
          onCancel={() => {
            root.unmount();
            document.body.removeChild(overlayContainer);
          }}
        />
      );
    } else if (request.action === "openKnowledgeCapture") {
      console.log("Received openKnowledgeCapture in content script:", request);
      await knowledgeManager.startCapture(
        request.screenshot,
        request.currentUrl
      );

      // If the message contains a pinId, store it in the URL for later retrieval
      if (request.pinId) {
        console.log(
          "Found pinId in openKnowledgeCapture message:",
          request.pinId
        );
        const url = new URL(window.location.href);
        url.searchParams.set("pinId", request.pinId);
        window.history.replaceState({}, "", url.toString());
        console.log("Updated URL with pinId:", url.toString());
      }
    }
  }
);

// Add this function to handle hover highlighting
function handleElementHover(element: HTMLElement) {
  // Only highlight if it's an interactive element
  if (isRecording) {
    // Remove previous highlight
    if (lastHighlightedElement) {
      lastHighlightedElement.style.outline = "";
      lastHighlightedElement.style.boxShadow = "";
    }

    // Add highlight to hovered element
    element.style.outline = "2px solid #ff0000";
    element.style.boxShadow = "0 0 10px rgba(255, 0, 0, 0.5)";
    lastHighlightedElement = element;
  }
}

// Add this function to handle hover exit
function handleElementLeave(element: HTMLElement) {
  if (isRecording && element === lastHighlightedElement) {
    element.style.outline = "";
    element.style.boxShadow = "";
    lastHighlightedElement = null;
  }
}

// Add mouseover and mouseout event listeners
document.addEventListener("mouseover", (e: MouseEvent) => {
  if (isRecording) {
    const target = e.target as HTMLElement;
    handleElementHover(target);
  }
});

document.addEventListener("mouseout", (e: MouseEvent) => {
  if (isRecording) {
    const target = e.target as HTMLElement;
    handleElementLeave(target);
  }
});

const getActionDescription = (
  target: HTMLElement
): { action: string; description: string } => {
  const tagName = target.tagName.toLowerCase();
  const text = target.textContent?.trim() || "";
  const role = target.getAttribute("role");
  const ariaLabel = target.getAttribute("aria-label");
  const title = target.getAttribute("title");

  // Get the most meaningful text content
  const displayText = ariaLabel || title || text;

  // Define element type and action
  let elementType: string;
  if (tagName === "svg" || tagName === "img" || role === "img") {
    elementType = "icon";
  } else if (tagName === "button" || role === "button") {
    elementType = "button";
  } else if (tagName === "a" || role === "link") {
    elementType = "link";
  } else if (tagName === "input") {
    const inputType = (target as HTMLInputElement).type;
    elementType =
      inputType === "button" || inputType === "submit" ? "button" : "input";
  } else if (role === "menuitem") {
    elementType = "menu item";
  } else if (role === "tab") {
    elementType = "tab";
  } else {
    elementType = "button"; // Default fallback
  }

  const action = `click`;
  const description = displayText
    ? `Click on ${elementType} "${displayText}"`
    : `Click on ${elementType}`;

  return { action, description };
};

function getQuerySelector(element: HTMLElement): string {
  if (element.id) {
    return `#${element.id}`;
  }

  const buildSelector = (el: HTMLElement): string => {
    let selector = el.tagName.toLowerCase();

    // Add ID if present
    if (el.id) {
      return `#${el.id}`;
    }

    // Add classes
    if (el.className) {
      const classes = Array.from(el.classList).join(".");
      if (classes) {
        selector += `.${classes}`;
      }
    }

    // Add nth-of-type if necessary
    let sibling = el.previousElementSibling;
    let index = 1;
    while (sibling) {
      if (sibling.tagName === el.tagName) {
        index++;
      }
      sibling = sibling.previousElementSibling;
    }
    if (index > 1) {
      selector += `:nth-of-type(${index})`;
    }

    return selector;
  };

  let path = [];
  let currentElement: HTMLElement | null = element;

  while (currentElement) {
    path.unshift(buildSelector(currentElement));
    if (currentElement.id) {
      break; // Stop if an ID is found
    }
    currentElement = currentElement.parentElement;
  }

  const selector = path.join(" > ");

  // Fallback to more specific selector if not unique
  return selector; // Could add more logic to refine
}

// Add click handler
document.addEventListener(
  "mousedown",
  async (e: MouseEvent) => {
    if (isRecording) {
      const target = e.target as HTMLElement;

      // Capture screenshot before the click
      const screenshot = await new Promise<string>((resolve) => {
        chrome.runtime.sendMessage({ action: "captureTab" }, resolve);
      });

      const { action, description } = getActionDescription(target);

      // Create and send the step with screenshot
      const step: Step = {
        action,
        element: target.tagName,
        text: target.textContent || undefined,
        timestamp: new Date().toISOString(),
        description,
        screenshot,
        querySelector: getQuerySelector(target),
        current_url: window.location.href,
      };
      chrome.runtime.sendMessage({ action: "addStep", step });
    }
  },
  { capture: true }
);

// Handle all input interactions
document.addEventListener("input", async (e: Event) => {
  if (!isRecording) return;

  const target = e.target as HTMLInputElement;

  // Skip if it's a submit/button input as those are handled by click handler
  if (target.type === "submit" || target.type === "button") {
    return;
  }

  const screenshot = await new Promise<string>((resolve) => {
    chrome.runtime.sendMessage({ action: "captureTab" }, resolve);
  });

  const step: Step = {
    action: "input",
    element: target.tagName,
    value: target.value,
    timestamp: new Date().toISOString(),
    description: target.value
      ? `Type "${target.value}" in ${target.tagName.toLowerCase()}`
      : `Type in ${target.tagName.toLowerCase()}`,
    screenshot,
    querySelector: getQuerySelector(target),
    current_url: window.location.href,
  };

  chrome.runtime.sendMessage({ action: "addInputStep", step });
});

// Handle Enter key in input fields
document.addEventListener("keydown", async (e: KeyboardEvent) => {
  if (!isRecording || e.key !== "Enter") return;

  const target = e.target as HTMLElement;
  if (target.tagName !== "INPUT") return;

  const inputElement = target as HTMLInputElement;
  if (inputElement.type === "submit" || inputElement.type === "button") return;

  // Only capture Enter key press if it's meant to submit/confirm the input
  if (
    e.isComposing || // Skip during IME composition
    inputElement.type === "search" || // Search inputs naturally use Enter
    target.closest("form") || // Input is part of a form
    inputElement.value.includes("\n") // Multiline input
  ) {
    const screenshot = await new Promise<string>((resolve) => {
      chrome.runtime.sendMessage({ action: "captureTab" }, resolve);
    });

    const step: Step = {
      action: "input",
      element: target.tagName,
      value: inputElement.value,
      timestamp: new Date().toISOString(),
      description: inputElement.value
        ? `Confirm "${inputElement.value}" in ${target.tagName.toLowerCase()} with Enter`
        : `Press Enter in ${target.tagName.toLowerCase()}`,
      screenshot,
      querySelector: getQuerySelector(target),
      current_url: window.location.href,
    };

    chrome.runtime.sendMessage({ action: "addInputStep", step });
  }
});

// Add message listener for auth token requests
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "REQUEST_AUTH_TOKEN") {
    // Post message to localhost page requesting the auth token
    window.postMessage(
      { type: "REQUEST_AUTH_TOKEN", source: "talknician_extension" },
      "*"
    );
  }
  if (request.action === "EXTENSION_INSTALLED") {
    window.postMessage(
      { type: "EXTENSION_INSTALLED", source: "talknician_extension" },
      "*"
    );
  }
  if (request.action === "VIDEO_PROCESSING_STARTED") {
    window.postMessage(
      {
        type: "VIDEO_PROCESSING_STARTED",
        source: "talknician_extension",
        process_id: request.process_id,
        message: request.message,
      },
      "*"
    );
  }
});

// Listen for response from localhost page
window.addEventListener("message", (event) => {
  // Verify the message source
  if (event.origin !== FE_URL) {
    return;
  }

  if (event.data.type === "AUTH_TOKEN_RESPONSE" && event.data.token) {
    // Send the token to the background script
    console.log("Received auth token from the page");
    setAuthToken(event.data.token);
  }
  if (event.data.type === "AUTH_TOKEN_REMOVE") {
    chrome.runtime.sendMessage({ action: "toggleToolbar", show: false });
    // Also hide the recording controls when hiding the toolbar due to auth token removal
    chrome.runtime.sendMessage({ action: "hideUI" });
    removeAuthToken();
    setRecordingState({
      isRecording: false,
      isPaused: false,
      recordingTime: "00:00",
      micAudioEnabled: true,
      systemAudioEnabled: true,
      captureType: "screen",
      isUIVisible: false,
    });
    // Update the global recording state first
  }
});

export default function Content() {
  return <div></div>;
}
