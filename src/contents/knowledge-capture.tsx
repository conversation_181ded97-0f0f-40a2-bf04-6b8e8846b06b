import type { <PERSON>ha<PERSON>, Too<PERSON> } from "../types";
import { CloseIcon, LogoIcon } from "../components/icons";
import { Toolbar } from "../components/Toolbar";
import { useCallback, useEffect, useState, useRef } from "react";
import { Canvas } from "../components/Canvas";
import { useStorage } from "@plasmohq/storage/hook";
import {
  knowledgeManager,
  type KnowledgeCapture,
} from "~background/knowledge-manager";
import type { PlasmoGetStyle } from "plasmo";
import styleText from "data-text:../styles/knowledge-capture.css";
import {
  fetchPin,
  saveKnowledgeCaptureQuickCapture,
  updatePin,
  uploadImage,
} from "~background/api";

export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style");
  style.textContent = styleText;
  return style;
};

function KnowledgeCapture() {
  const [shapes, setShapes] = useState<Shape[]>([]);
  const [selectedShape, setSelectedShape] = useState<Shape | null>(null);
  const [currentTool, setCurrentTool] = useState<Tool>("cursor");
  const [description, setDescription] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [currentColor, setCurrentColor] = useState("#ff0000");
  const [history, setHistory] = useState<Shape[][]>([[]]);
  const [futureHistory, setFutureHistory] = useState<Shape[][]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useStorage<boolean>(
    {
      key: "knowledge_capture_visible",
      instance: knowledgeManager.getStorage(),
    },
    false
  );
  const [currentCapture] = useStorage<KnowledgeCapture>(
    {
      key: "current_capture",
      instance: knowledgeManager.getStorage(),
    },
    {
      id: "",
      screenshot: "",
      description: "",
      shapes: [],
      createdAt: "",
    }
  );
  const [knowledgeCaptures, setKnowledgeCaptures] = useStorage<
    KnowledgeCapture[]
  >(
    {
      key: "knowledge_captures",
      instance: knowledgeManager.getStorage(),
    },
    []
  );
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentTabId, setCurrentTabId] = useState<number | null>(null);

  // Get the current tab ID when component mounts
  useEffect(() => {
    const getCurrentTabId = async () => {
      // We need to use chrome.runtime.sendMessage to get the tab ID from the background script
      chrome.runtime.sendMessage({ action: "getCurrentTabId" }, (response) => {
        if (response && response.tabId) {
          setCurrentTabId(response.tabId);
        }
      });
    };

    getCurrentTabId();

    // Listen for messages from the background script
    const messageListener = (
      message: any,
      _sender: any,
      _sendResponse: any
    ) => {
      // If the message contains a pinId, store it for later use
      if (message.action === "openKnowledgeCapture") {
        console.log("Received openKnowledgeCapture message:", message);

        if (message.pinId) {
          console.log("Found pinId in message:", message.pinId);
          // Store the pinId in the URL for later retrieval
          const url = new URL(window.location.href);
          url.searchParams.set("pinId", message.pinId);
          window.history.replaceState({}, "", url.toString());
          console.log("Updated URL with pinId:", url.toString());
        } else {
          console.log("No pinId found in openKnowledgeCapture message");
        }
      }
      return true;
    };

    chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, []);

  // Check visibility based on tab ID
  useEffect(() => {
    if (currentTabId === null) return;

    const checkVisibility = async () => {
      const isVisible = await knowledgeManager.checkVisibility(currentTabId);
      setIsVisible(isVisible);

      if (!isVisible) {
        knowledgeManager.clearCurrentCapture();
      }
    };

    checkVisibility();

    // Listen for tab focus changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        // When tab loses focus, check if we should hide the capture
        checkVisibility();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [currentTabId]);

  // Update shapes and description when capture changes
  useEffect(() => {
    if (currentCapture) {
      setShapes(currentCapture.shapes || []);
      setDescription(currentCapture.description || "");
    }
  }, [currentCapture]);

  // Update capture in storage when shapes or description change
  useEffect(() => {
    if (currentCapture) {
      knowledgeManager.updateCapture({
        shapes,
        description,
      });
    }
  }, [shapes, description, currentCapture]);

  const handleClose = useCallback(() => {
    knowledgeManager.clearCurrentCapture();
  }, []);

  const handleSave = useCallback(async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    setError(null);

    try {
      if (!canvasRef.current) {
        throw new Error("Canvas not found");
      }

      // Get the data URL directly from canvas
      // Upload the screenshot
      const response = await fetch(canvasRef.current.toDataURL("image/png"));
      const blob = await response.blob();
      const imageUrl = await uploadImage(blob);

      const completedCapture = {
        ...currentCapture,
        description: description,
        screenshot: imageUrl,
        current_url: window.location.href,
      };

      // Save the knowledge capture
      const knowledgeCapture =
        await saveKnowledgeCaptureQuickCapture(completedCapture);
      setKnowledgeCaptures([knowledgeCapture, ...knowledgeCaptures]);

      // Automatically link the knowledge capture to the current pin if it exists

      // Get the current pin ID from the URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      console.log("URL search params:", window.location.search);
      const pinId = urlParams.get("pinId");
      console.log("Retrieved pinId from URL:", pinId);

      // If we have a pin ID and the knowledge capture was saved successfully,
      // automatically link it to the current pin
      if (pinId && knowledgeCapture && knowledgeCapture.uuid) {
        console.log(
          `Automatically linking knowledge capture ${knowledgeCapture.uuid} to pin ${pinId}`
        );

        try {
          // First, fetch the current pin data to ensure we have the latest
          const currentPin = await fetchPin(pinId);
          console.log("Fetched current pin data:", currentPin);

          // Check if the knowledge capture is already linked
          const existingLinkedKnowledge = currentPin.linkedKnowledge || [];
          if (existingLinkedKnowledge.includes(knowledgeCapture.uuid)) {
            console.log("Knowledge capture already linked to pin");
          } else {
            // Add the knowledge capture to the pin's linkedKnowledge array
            const updatedLinkedKnowledge = [
              ...existingLinkedKnowledge,
              knowledgeCapture.uuid,
            ];
            console.log(
              "Updated linkedKnowledge array:",
              updatedLinkedKnowledge
            );

            // Update the pin directly using the API
            const updatedPin = await updatePin(pinId, {
              ...currentPin,
              linkedKnowledge: updatedLinkedKnowledge,
            });
            console.log("Pin updated successfully:", updatedPin);

            // Send multiple notifications to ensure the UI updates
            // 1. Send a message to the background script
            chrome.runtime.sendMessage({
              action: "pinUpdated",
              pin: updatedPin,
            });

            // 2. Also notify any parent windows or tabs that might be displaying the pin
            if (window.opener) {
              window.opener.postMessage(
                {
                  action: "pinUpdated",
                  pin: updatedPin,
                },
                "*"
              );
            }

            // 3. Broadcast to all tabs
            chrome.runtime.sendMessage({
              action: "linkKnowledgeToPin",
              pinId: pinId,
              knowledgeId: knowledgeCapture.uuid,
            });

            // 4. Wait a moment and send another update to ensure it's processed
            setTimeout(() => {
              chrome.runtime.sendMessage({
                action: "pinUpdated",
                pin: updatedPin,
              });
            }, 500);
          }
        } catch (error) {
          console.error("Error linking knowledge capture to pin:", error);

          // Fallback to the original method if direct API update fails
          chrome.runtime.sendMessage(
            {
              action: "linkKnowledgeToPin",
              pinId: pinId,
              knowledgeId: knowledgeCapture.uuid,
            },
            (response) => {
              if (response && response.success) {
                console.log(
                  "Successfully linked knowledge capture to pin using fallback method"
                );

                if (response.pin) {
                  // Send multiple notifications
                  chrome.runtime.sendMessage({
                    action: "pinUpdated",
                    pin: response.pin,
                  });

                  if (window.opener) {
                    window.opener.postMessage(
                      {
                        action: "pinUpdated",
                        pin: response.pin,
                      },
                      "*"
                    );
                  }
                }
              } else {
                console.error(
                  "Failed to link knowledge capture to pin:",
                  response?.error
                );
              }
            }
          );
        }
      }

      // Update the current capture with the new screenshot
      setIsVisible(false);

      // Clear the capture
      knowledgeManager.clearCurrentCapture();
    } catch (error) {
      console.error("Failed to save capture:", error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("Failed to save capture. Please try again.");
      }
    } finally {
      setDescription("");
      setIsSubmitting(false);
    }
  }, [isSubmitting, description, currentCapture, knowledgeCaptures]);

  const handleHistoryUpdate = useCallback((newShapes: Shape[]) => {
    setHistory((prev) => [...prev, newShapes]);
    setFutureHistory([]);
  }, []);

  const handleUndo = useCallback(() => {
    if (history.length > 1) {
      const newHistory = [...history];
      const lastState = newHistory.pop()!;
      setHistory(newHistory);
      setFutureHistory((prev) => [lastState, ...prev]);
      setShapes(newHistory[newHistory.length - 1]);
    }
  }, [history]);

  const handleRedo = useCallback(() => {
    if (futureHistory.length > 0) {
      const [nextState, ...remainingFuture] = futureHistory;
      setHistory((prev) => [...prev, nextState]);
      setFutureHistory(remainingFuture);
      setShapes(nextState);
    }
  }, [futureHistory]);

  const handleDeleteShape = useCallback(() => {
    if (selectedShape) {
      setShapes((prevShapes) => {
        const updatedShapes = prevShapes.filter((s) => s !== selectedShape);
        setHistory((prev) => [...prev, updatedShapes]);
        return updatedShapes;
      });
      setSelectedShape(null);
    }
  }, [selectedShape]);

  if (!isVisible || !currentCapture) return <div></div>;

  return (
    <div
      className="knowledge-capture"
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
      onKeyDown={(e) => e.stopPropagation()}
    >
      <div className="capture-container">
        <div className="capture-header">
          <div className="header-left">
            <div className="header-icon">
              <LogoIcon />
            </div>
            <h1 className="header-title">Quick Capture</h1>
          </div>
          <button
            className="close-button"
            onClick={(e) => {
              e.stopPropagation();
              handleClose();
            }}
          >
            <CloseIcon />
          </button>
        </div>

        <Toolbar
          selectedTool={currentTool}
          onToolSelect={setCurrentTool}
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={history.length > 1}
          canRedo={futureHistory.length > 0}
          onDelete={handleDeleteShape}
          currentColor={currentColor}
          onColorChange={setCurrentColor}
        />

        <div className="canvas-container">
          <Canvas
            ref={canvasRef}
            screenshot={currentCapture.screenshot}
            shapes={shapes}
            selectedShape={selectedShape}
            currentTool={currentTool}
            currentColor={currentColor}
            isEditing={isEditing}
            onShapeUpdate={setShapes}
            onSelectedShapeChange={setSelectedShape}
            onStartTextEditing={(shape) => {
              setIsEditing(true);
              setSelectedShape(shape);
            }}
            onFinishTextEditing={() => {
              setIsEditing(false);
              setCurrentTool("cursor");
            }}
            onToolChange={setCurrentTool}
            onHistoryUpdate={handleHistoryUpdate}
            onUndo={handleUndo}
            onRedo={handleRedo}
          />
        </div>

        <footer className="capture-footer">
          <div className="description-container">
            <label className="description-label">Description (optional)</label>
            <textarea
              value={description}
              onChange={(e) => {
                e.stopPropagation();
                setDescription(e.target.value);
              }}
              onKeyDown={(e) => e.stopPropagation()}
              onClick={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              placeholder="Add notes about your capture..."
              className="description-textarea"
            />
          </div>
          <div className="create-button-container">
            {error && <div className="error-message">{error}</div>}
            <button
              className="create-button"
              onClick={(e) => {
                e.stopPropagation();
                handleSave();
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Creating..." : "Create Capture"}
            </button>
          </div>
        </footer>
      </div>
    </div>
  );
}

export default KnowledgeCapture;
