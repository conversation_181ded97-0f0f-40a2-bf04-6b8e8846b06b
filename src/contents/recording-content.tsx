import React, { useEffect } from "react";
import { useRecordingState } from "~hooks/useRecordingState";
import useDraggable from "~hooks/useDraggable";
import RecordingControls from "~components/recording/RecordingControls";
import type { PlasmoGetStyle } from "plasmo";
import styleText from "data-text:~styles/globals.css";

/**
 * Style injection for Plasmo extension
 */
export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style");
  style.textContent = styleText;
  return style;
};

/**
 * RecordingContent component
 *
 * Provides a full-screen overlay with recording controls
 * that allow users to start, pause, resume, stop, reset, and delete recordings.
 */
const RecordingContent: React.FC = () => {
  const { recordingState, stopRecording, startRecording } = useRecordingState();
  const draggable = useDraggable();

  useEffect(() => {
    if (!recordingState.isUIVisible) {
      draggable.resetPostition();
    }
  }, [recordingState.isUIVisible]);

  // Handle escape key to cancel recording
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && recordingState.isRecording) {
        stopRecording();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [recordingState.isRecording, stopRecording]);

  // Handle beforeunload to warn user if recording is in progress
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (recordingState.isRecording) {
        // Modern approach for browser compatibility
        e.preventDefault();
        // For older browsers
        const message =
          "You have an active recording. Are you sure you want to leave?";
        e.preventDefault();
        // Note: Setting returnValue is deprecated but still needed for some browsers
        // Using void operator to avoid linting warnings
        void (e.returnValue = message);
        return message;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [recordingState.isRecording]);

  // Only render if UI is visible
  if (!recordingState.isUIVisible) {
    return null;
  }

  return (
    <div
      ref={draggable.ref}
      onMouseDown={draggable.onMouseDown}
      style={{ ...draggable.style }}
      className="fixed bottom-[100px] left-[100px] top-auto right-auto z-50 w-[350px] h-[80px]"
    >
      {!recordingState.isRecording ? (
        <div className="relative bg-[#1A1D24]/90 rounded-full py-[8px] px-[20px] shadow-lg backdrop-blur-sm cursor-grab select-none max-w-[567px] mx-auto border border-[#252830]">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div className="font-mono text-[14px] font-bold text-white min-w-[120px]">
                Ready to record
              </div>
            </div>
            <div className="ml-4">
              <button
                className="bg-[#1A1D24] hover:bg-[#252830] text-[#00E5C7] text-[14px] font-medium rounded-full px-[16px] py-[8px] transition-all duration-200 border border-[#00E5C7]"
                onClick={startRecording}
                title="Start Recording"
              >
                Start
              </button>
            </div>
          </div>
        </div>
      ) : (
        <RecordingControls />
      )}
    </div>
  );
};

export default RecordingContent;
