import { useEffect, useState, useRef } from "react";
import { TimeMachineIcon, LogoIcon, PlusIcon } from "~components/icons";
import { useStorage } from "@plasmohq/storage/hook";
import type { PlasmoGetStyle } from "plasmo";
import styleText from "data-text:../styles/houston-chat.css";
import type { ChatHistory, ChatMessage } from "~background/houston-manager";
import { houstonManager } from "~background/houston-manager";
import type { HoustonResponse } from "~background/api";

interface Metadata {
  type: string;
  title: string;
  url: string;
  source: string;
  last_updated?: string;
  duration?: string;
  thumbnail?: string;
  publisher?: string;
  size?: {
    width: number;
    height: number;
  };
  source_website?: string;
}

interface WebResult {
  metadata: Metadata;
}
interface Source {
  source_url: string;
  filename: string;
  page_number: number;
  relevance_score: number;
}

interface InternetResults {
  webpages: WebResult[];
  videos: WebResult[];
  images: WebResult[];
}

export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style");
  style.textContent = styleText;
  return style;
};

const formatText = (text: string) => {
  return text.split("\n").map((line, i) => (
    <p key={i} className="message-paragraph">
      {line || "\u00A0"}
    </p>
  ));
};

function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }

  return date.toLocaleDateString();
}

export default function HoustonChat() {
  const [query, setQuery] = useState("");
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [currentChat, setCurrentChat] = useState<ChatMessage[]>([]);
  const [selectedChatId, setSelectedChatId] = useStorage<string | null>(
    "current_chat_id",
    null
  );
  const [isVisible, setIsVisible] = useStorage("houston_chat_visible", false);
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [local_document_text_sources, setLocalDocumentTextSources] = useState<
    Source[]
  >([]);
  const [internetResults, setInternetResults] =
    useState<InternetResults | null>(null);
  const chatContentRef = useRef<HTMLDivElement>(null);
  const [showAllSources, setShowAllSources] = useState<{
    local: boolean;
    web: boolean;
    video: boolean;
  }>({
    local: false,
    web: false,
    video: false,
  });
  const [internetSearchEnabled, setInternetSearchEnabled] = useState(true);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  const scrollToBottom = () => {
    if (chatContentRef.current) {
      chatContentRef.current.scrollTo({
        top: chatContentRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentChat]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        setIsVisible(false);
      }
    };

    const checkVisibility = async () => {
      const isVisible = await houstonManager.checkVisibility(
        window.location.href
      );
      setIsVisible(isVisible);
    };

    // Check visibility on mount and when URL changes
    checkVisibility();

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [setIsVisible]);

  useEffect(() => {
    if (isVisible) {
      // Fetch chat history from background
      chrome.runtime.sendMessage({ action: "fetchChatHistory" }, (response) => {
        if (response.success) {
          setChatHistory(response.history);
        } else {
          console.error("Error fetching chat history:", response.error);
        }
      });
    }
  }, [isVisible]);

  useEffect(() => {
    if (selectedChatId) {
      setCurrentChat([]);

      // Fetch chat content from background
      chrome.runtime.sendMessage(
        { action: "fetchChatContent", chatId: selectedChatId },
        (response) => {
          if (response.success) {
            setCurrentChat(response.content);
            if (response.sources) {
              setLocalDocumentTextSources(
                response.sources.local_document_text_sources || []
              );
              setInternetResults(response.sources.internet_results);
            } else {
              setLocalDocumentTextSources([]);
              setInternetResults(null);
            }
          } else {
            console.error("Error fetching chat content:", response.error);
          }
        }
      );
    }
  }, [selectedChatId]);

  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isVisible) {
        setIsVisible(false);
      }
    };

    document.addEventListener("keydown", handleEscapeKey);
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [isVisible, setIsVisible]);

  useEffect(() => {
    const interval = setInterval(() => {
      setLastRefresh(Date.now());
    }, 60000); // Every minute

    return () => clearInterval(interval);
  }, []);

  const handleChatSelection = (chatId: string) => {
    setSelectedChatId(chatId);
    if (chatId !== selectedChatId) {
      setInternetResults(null);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  const handleNewChat = () => {
    setSelectedChatId(null);
    setCurrentChat([]);
    setQuery("");
    setInternetResults(null);
  };

  const fetchChatHistory = () => {
    chrome.runtime.sendMessage({ action: "fetchChatHistory" }, (response) => {
      if (response.success) {
        setChatHistory(response.history);
      } else {
        console.error("Error fetching chat history:", response.error);
      }
    });
  };

  const handleSendMessage = async () => {
    if (!query.trim()) return;

    const optimisticMessage = {
      role: "user",
      content: query.trim(),
    };

    const loadingMessage = {
      role: "assistant",
      content: "...",
      isLoading: true,
    };

    setCurrentChat((prev) => [...prev, optimisticMessage, loadingMessage]);
    setQuery("");
    scrollToBottom();

    try {
      const response = (await chrome.runtime.sendMessage({
        action: "sendMessage",
        query: query.trim(),
        internetSearch: internetSearchEnabled,
      })) as { success: boolean; response: HoustonResponse };

      if (response.success) {
        setCurrentChat(response.response.chat_history);
        setSuggestedQuestions(response.response.suggested_questions);
        setSelectedChatId(response.response.chat_id);
        if (response.response.local_document_text_sources) {
          setLocalDocumentTextSources(
            response.response.local_document_text_sources
          );
        }
        if (response.response.internet_results) {
          setInternetResults(response.response.internet_results);
        } else {
          setInternetResults(null);
        }
        // Fetch updated chat history after sending a message
        if (!selectedChatId) {
          fetchChatHistory();
        }
      }
    } catch (error) {
      console.error("Error sending message:", error);
      // Remove loading message on error
      setCurrentChat((prev) => prev.filter((msg) => !("isLoading" in msg)));
    }
  };

  const handleContainerClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleInputFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.stopPropagation();
    e.currentTarget.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    e.stopPropagation();
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handlePlusClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowSuggestions(!showSuggestions);
  };

  const renderMessage = (message: ChatMessage) => {
    if (message.role === "assistant") {
      return (
        <div
          className={`message-content ${"isLoading" in message ? "loading-message" : ""}`}
        >
          {"isLoading" in message
            ? message.content
            : formatText(message.content)}
        </div>
      );
    }
    return message.content;
  };

  if (!isVisible) return null;

  return (
    <div
      className="chat-container"
      tabIndex={0}
      onClick={handleContainerClick}
      onMouseDown={(e) => e.stopPropagation()}
    >
      <button
        className="close-button"
        onClick={handleClose}
        aria-label="Close chat"
      />
      <div className="sidebar">
        <div className="brand">
          <LogoIcon />
          <span>Talknician</span>
        </div>

        <button className="new-chat-button" onClick={handleNewChat}>
          <PlusIcon />
          <span>New Chat</span>
        </button>

        <div className="sidebar-section">
          <div className="section-header">
            <TimeMachineIcon />
            <h3 className="chat-section-header">History</h3>
          </div>
          <ul className="history-list">
            {chatHistory.map((item, index) => (
              <li
                key={index}
                className={`history-item ${item.chat_id === selectedChatId ? "active" : ""}`}
                onClick={() => handleChatSelection(item.chat_id)}
              >
                <div className="history-item-content">
                  <span className="history-item-time">
                    Chat from {formatRelativeTime(item.created_at)}
                  </span>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <main className="main-content">
        <div className="chat-content" ref={chatContentRef}>
          {currentChat.map((message, index) => (
            <div key={index} className={`message ${message.role}`}>
              {renderMessage(message)}
            </div>
          ))}
        </div>
        <div className="follow-up">
          <div className="input-container">
            <div className="plus-icon-container" onClick={handlePlusClick}>
              <PlusIcon className="plus-icon" />
            </div>
            <input
              type="text"
              placeholder="Ask follow-up"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={handleInputFocus}
              onClick={(e) => e.stopPropagation()}
              className="chat-input"
            />
            <button
              onClick={() => setInternetSearchEnabled(!internetSearchEnabled)}
              className={`internet-search-toggle ${internetSearchEnabled ? "enabled" : ""}`}
              style={{
                padding: "4px 8px",
                marginLeft: "8px",
                backgroundColor: internetSearchEnabled ? "#007AFF" : "#6B7280",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "12px",
                display: "flex",
                alignItems: "center",
                gap: "4px",
              }}
              title={`Internet search is ${internetSearchEnabled ? "enabled" : "disabled"}`}
            >
              <span style={{ fontSize: "14px" }}>🌐</span>
              {internetSearchEnabled ? "On" : "Off"}
            </button>
          </div>
          {showSuggestions && (
            <div className="suggested-questions">
              {suggestedQuestions.map((question, index) => (
                <button
                  key={index}
                  className="suggested-question"
                  onClick={() => {
                    setQuery(question);
                    setShowSuggestions(false);
                  }}
                >
                  {question}
                </button>
              ))}
            </div>
          )}
        </div>
      </main>

      <div className="sidebar right-sidebar">
        <div className="sidebar-section">
          <div className="section-header">
            <h3 className="chat-section-header">Sources</h3>
          </div>
          <div className="sources-container">
            {local_document_text_sources.length > 0 && (
              <>
                <div className="source-group">
                  <h4 className="source-group-title">Local Sources</h4>
                  {local_document_text_sources
                    .slice(0, showAllSources.local ? undefined : 5)
                    .map((source, index) => (
                      <div key={index} className="source-item">
                        <a
                          href={source.source_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="source-link"
                        >
                          <span className="source-title">
                            {source.filename}
                          </span>
                        </a>
                      </div>
                    ))}
                  {local_document_text_sources.length > 5 && (
                    <button
                      className="show-more-button"
                      onClick={() =>
                        setShowAllSources((prev) => ({
                          ...prev,
                          local: !prev.local,
                        }))
                      }
                    >
                      {showAllSources.local ? "Show Less" : "Show More"}
                    </button>
                  )}
                </div>
              </>
            )}

            {internetResults?.webpages &&
              internetResults.webpages.length > 0 && (
                <>
                  <div className="source-group">
                    <h4 className="source-group-title">Web Sources</h4>
                    {internetResults.webpages
                      .slice(0, showAllSources.web ? undefined : 5)
                      .map((result, index) => (
                        <div key={index} className="source-item">
                          <a
                            href={result.metadata.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="source-link"
                          >
                            <span className="source-title">
                              {result.metadata.title}
                            </span>
                          </a>
                        </div>
                      ))}
                    {internetResults.webpages.length > 5 && (
                      <button
                        className="show-more-button"
                        onClick={() =>
                          setShowAllSources((prev) => ({
                            ...prev,
                            web: !prev.web,
                          }))
                        }
                      >
                        {showAllSources.web ? "Show Less" : "Show More"}
                      </button>
                    )}
                  </div>
                </>
              )}

            {internetResults?.videos && internetResults.videos.length > 0 && (
              <>
                <div className="source-group">
                  <h4 className="source-group-title">Video Sources</h4>
                  {internetResults.videos
                    .slice(0, showAllSources.video ? undefined : 5)
                    .map((result, index) => (
                      <div key={index} className="source-item video-source">
                        <a
                          href={result.metadata.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="source-link"
                        >
                          <span className="source-title">
                            {result.metadata.title}
                          </span>
                        </a>
                      </div>
                    ))}
                  {internetResults.videos.length > 5 && (
                    <button
                      className="show-more-button"
                      onClick={() =>
                        setShowAllSources((prev) => ({
                          ...prev,
                          video: !prev.video,
                        }))
                      }
                    >
                      {showAllSources.video ? "Show Less" : "Show More"}
                    </button>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
