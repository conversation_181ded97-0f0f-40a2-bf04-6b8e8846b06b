import { Storage } from "@plasmohq/storage";
import iconBase64 from "data-base64:~assets/icon.png";
const storage = new Storage({
  area: "local",
});

// Debounce utility
const debounce = (func: Function, wait: number) => {
  let timeout: ReturnType<typeof setTimeout> | undefined;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Size threshold for large elements (in pixels)
const LARGE_ELEMENT_THRESHOLD = {
  width: 500,
  height: 300,
};

// Get a unique selector for the clicked element
function getUniqueSelector(element: Element): string {
  if (element.id) return `#${element.id}`;

  // If the element has classes, use them
  if (element.classList.length > 0) {
    const classSelector = Array.from(element.classList)
      .map((c) => `.${c}`)
      .join("");

    // Check if this selector uniquely identifies the element
    if (document.querySelectorAll(classSelector).length === 1) {
      return classSelector;
    }
  }

  // Fallback to a position-based selector
  const path = [];
  let currentElement: Element | null = element;

  while (currentElement && currentElement !== document.body) {
    let selector = currentElement.tagName.toLowerCase();

    if (currentElement.parentElement) {
      const siblings = Array.from(currentElement.parentElement.children);
      const index = siblings.indexOf(currentElement) + 1;
      selector += `:nth-child(${index})`;
    }

    path.unshift(selector);
    currentElement = currentElement.parentElement;
  }

  return path.join(" > ");
}

// Check if an element is considered "large"
function isLargeElement(element: Element): boolean {
  const rect = element.getBoundingClientRect();
  return (
    rect.width > LARGE_ELEMENT_THRESHOLD.width ||
    rect.height > LARGE_ELEMENT_THRESHOLD.height
  );
}

// Calculate relative position as percentage of document size
function calculateRelativePosition(
  x: number,
  y: number
): { xPercent: number; yPercent: number } {
  // Use document dimensions rather than viewport dimensions
  const documentWidth = Math.max(
    document.documentElement.scrollWidth,
    document.body.scrollWidth
  );
  const documentHeight = Math.max(
    document.documentElement.scrollHeight,
    document.body.scrollHeight
  );

  return {
    xPercent: (x / documentWidth) * 100,
    yPercent: (y / documentHeight) * 100,
  };
}

// Convert relative position to absolute document position
function getAbsolutePosition(
  xPercent: number,
  yPercent: number
): { x: number; y: number } {
  // Use document dimensions rather than viewport dimensions
  const documentWidth = Math.max(
    document.documentElement.scrollWidth,
    document.body.scrollWidth
  );
  const documentHeight = Math.max(
    document.documentElement.scrollHeight,
    document.body.scrollHeight
  );

  return {
    x: (xPercent * documentWidth) / 100,
    y: (yPercent * documentHeight) / 100,
  };
}

// Import API functions
import {
  createPin,
  updatePin,
  deletePin,
  fetchAllPins,
  uploadImage,
} from "~background/api";
import type { PinResponse } from "~background/api";

// Interface for storing pin data
interface Pin {
  uuid?: string; // UUID for API integration
  id?: number; // ID from API
  selector?: string; // For element-based pins
  xPercent?: number; // For coordinate-based pins (% of viewport width)
  yPercent?: number; // For coordinate-based pins (% of viewport height)
  description: string;
  timestamp: number;
  url: string;
  isCoordinateBased: boolean;
  screenshot?: string; // Optional screenshot URL
  // Additional metadata
  pageTitle?: string;
  browser?: string;
  os?: string;
  resolution?: string;
  browserWindow?: string;
  colorDepth?: string;
  clientTimestamp?: string;
  linkedGuides?: string[];
  linkedKnowledge?: string[];
  creator_email?: string;
  is_favorite?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Get pins for the current URL
async function getPinsForCurrentPage(): Promise<Pin[]> {
  try {
    // Get the current URL and normalize it
    const currentUrl = window.location.href;

    // Normalize URL for comparison
    const normalizeUrl = (url: string): string => {
      try {
        // Create URL object to parse the URL
        const urlObj = new URL(url);

        // Get the pathname and normalize it
        let pathname = urlObj.pathname;
        // Remove trailing slash if present
        pathname = pathname.replace(/\/$/, "");

        // Return the normalized URL (hostname + pathname)
        return (urlObj.hostname + pathname).toLowerCase();
      } catch (e) {
        // If URL parsing fails, return the original URL
        return url.toLowerCase();
      }
    };

    const normalizedCurrentUrl = normalizeUrl(currentUrl);

    // Fetch all pins from the API
    const allPins = await fetchAllPins();

    // Filter pins for the current URL
    const pinsForCurrentPage = allPins.filter((pin) => {
      const normalizedPinUrl = normalizeUrl(pin.url);
      return normalizedPinUrl === normalizedCurrentUrl;
    });

    return pinsForCurrentPage;
  } catch (error) {
    console.error("Error getting pins for current page:", error);
    return [];
  }
}

// Function to capture screenshot and upload it to get an image URL
async function captureScreenshot(): Promise<string | null> {
  try {
    // Use chrome.tabs API to capture screenshot
    const dataUrl = await new Promise<string | null>((resolve) => {
      chrome.runtime.sendMessage(
        { action: "captureScreenshot" },
        (response) => {
          if (chrome.runtime.lastError) {
            console.error(
              "Error capturing screenshot:",
              chrome.runtime.lastError
            );
            resolve(null);
          } else if (response && response.dataUrl) {
            resolve(response.dataUrl);
          } else {
            resolve(null);
          }
        }
      );
    });

    if (!dataUrl) {
      return null;
    }

    // Convert the data URL to a blob and upload it
    try {
      // Convert base64 to blob
      const response = await fetch(dataUrl);
      const blob = await response.blob();

      // Upload the blob to get an image URL
      const imageUrl = await uploadImage(blob);
      console.log("Screenshot uploaded, image URL:", imageUrl);
      return imageUrl;
    } catch (uploadError) {
      console.error("Error uploading screenshot:", uploadError);
      return null;
    }
  } catch (error) {
    console.error("Error capturing screenshot:", error);
    return null;
  }
}

// Save a pin
async function savePin(pinData: Pin): Promise<Pin> {
  // Get the current URL and normalize it
  const originalUrl = pinData.url;

  // Use the same normalization function as getPinsForCurrentPage
  const normalizeUrl = (url: string): string => {
    try {
      // Just remove the hash part for saving
      return url.split("#")[0];
    } catch (e) {
      // If parsing fails, return the original URL
      return url;
    }
  };

  const normalizedUrl = normalizeUrl(originalUrl);
  console.log("Saving pin to URL:", normalizedUrl);

  const allPins =
    (await storage.get<{ [url: string]: Pin[] }>("talknician_pins")) || {};

  if (!allPins[normalizedUrl]) {
    allPins[normalizedUrl] = [];
  }

  // Get system information for the pin
  const os =
    navigator.userAgent.match(/Windows NT|Mac OS X|Linux|Android|iOS/i)?.[0] ||
    "Unknown";
  const browser =
    navigator.userAgent.match(/chrome|firefox|safari|edge|opera/i)?.[0] ||
    "Unknown";
  const browserVersion =
    navigator.userAgent.match(
      /(?:chrome|firefox|safari|edge|opera)\/(\d+(\.\d+)?)/i
    )?.[1] || "";
  const resolution = `${window.screen.width} x ${window.screen.height} px`;
  const browserWindow = `${window.innerWidth} x ${window.innerHeight} px`;
  const colorDepth = `${window.screen.colorDepth} bit`;
  const browserInfoFormatted = `${browser.charAt(0).toUpperCase() + browser.slice(1)} ${browserVersion}`;
  const osFormatted = `${os.replace(/windows nt/i, "Windows").replace(/mac os x/i, "Mac OS")} ${navigator.userAgent.match(/NT (\d+\.\d+)|Mac OS X (\d+[._]\d+)/i)?.[0]?.replace("NT ", "") || ""}`;

  // Capture screenshot
  const screenshot = await captureScreenshot();

  // Create final pin with all metadata
  const pinToSave = {
    ...pinData,
    // Always use normalized URL
    url: normalizedUrl,
    timestamp: pinData.timestamp || Date.now(),
    pageTitle: document.title || "",
    browser: browserInfoFormatted,
    os: osFormatted,
    resolution,
    browserWindow,
    colorDepth,
    clientTimestamp: new Date().toISOString(),
    linkedGuides: pinData.linkedGuides || [],
    linkedKnowledge: pinData.linkedKnowledge || [],
    screenshot: screenshot || undefined,
  };

  try {
    let savedPin: PinResponse;

    if (pinData.uuid) {
      // Update existing pin
      console.log(`Updating existing pin with UUID: ${pinData.uuid}`);
      savedPin = await updatePin(pinData.uuid, pinToSave);
    } else {
      // Create new pin
      console.log("Creating new pin");
      savedPin = await createPin(pinToSave);
    }

    console.log("Pin saved successfully to API:", savedPin);
    return savedPin;
  } catch (error) {
    console.error("Error saving pin to API:", error);
    throw error;
  }
}

// Remove a pin
async function removePin(pin: Pin): Promise<void> {
  console.log("Removing pin:", pin.description);

  if (!pin.uuid) {
    console.warn("Cannot remove pin without UUID");
    return;
  }

  try {
    const response = await deletePin(pin.uuid);
    console.log("Pin removed successfully:", response.message);
  } catch (error) {
    console.error(`Error removing pin with UUID ${pin.uuid}:`, error);
    throw error;
  }
}

// Simple popup for entering description
function createDescriptionPopup(
  top: number,
  left: number,
  existingDescription: string | null,
  callback: (description: string) => void
): void {
  // Check if popup already exists and remove it
  const existingPopup = document.getElementById("talknician-description-popup");
  if (existingPopup) {
    document.body.removeChild(existingPopup);
  }

  // Create popup
  const popup = document.createElement("div");
  popup.id = "talknician-description-popup";
  popup.style.position = "fixed";
  popup.style.top = `${top}px`;
  popup.style.left = `${left}px`;
  popup.style.zIndex = "999999";
  popup.style.background = "#1a1a1a";
  popup.style.border = "2px solid #40F7FF";
  popup.style.borderRadius = "8px";
  popup.style.padding = "12px";
  popup.style.width = "300px";

  // Description textarea
  const textarea = document.createElement("textarea");
  textarea.placeholder = "Pin name...";
  textarea.style.width = "100%";
  textarea.style.maxWidth = "300px";
  textarea.style.minHeight = "60px";
  textarea.style.background = "#2a2a2a";
  textarea.style.color = "#ffffff";
  textarea.style.border = "1px solid #4a4a4a";
  textarea.style.borderRadius = "6px";
  textarea.style.padding = "8px";
  textarea.style.margin = "0 0 10px 0";
  textarea.style.resize = "vertical";
  textarea.value = existingDescription || "";
  popup.appendChild(textarea);

  // Button container
  const buttonContainer = document.createElement("div");
  buttonContainer.style.display = "flex";
  buttonContainer.style.justifyContent = "space-between";

  // Save button
  const saveButton = document.createElement("button");
  saveButton.textContent = "Save";
  saveButton.style.background = "#40F7FF";
  saveButton.style.color = "#000000";
  saveButton.style.border = "none";
  saveButton.style.padding = "8px 12px";
  saveButton.style.borderRadius = "4px";
  saveButton.style.cursor = "pointer";
  saveButton.style.fontWeight = "bold";
  saveButton.addEventListener("click", () => {
    // First remove the popup
    document.body.removeChild(popup);

    // Immediately turn off pinning mode to prevent further interactions
    togglePinMode(false);

    // Then call the callback with the description
    callback(textarea.value);
  });
  buttonContainer.appendChild(saveButton);

  // Cancel button
  const cancelButton = document.createElement("button");
  cancelButton.textContent = "Cancel";
  cancelButton.style.background = "#666";
  cancelButton.style.color = "#ffffff";
  cancelButton.style.border = "none";
  cancelButton.style.padding = "8px 12px";
  cancelButton.style.borderRadius = "4px";
  cancelButton.style.cursor = "pointer";
  cancelButton.addEventListener("click", () => {
    document.body.removeChild(popup);
    togglePinMode(false);
  });
  buttonContainer.appendChild(cancelButton);

  popup.appendChild(buttonContainer);
  document.body.appendChild(popup);

  // Focus the textarea
  setTimeout(() => textarea.focus(), 0);
}

// Add marker to an element
function markElement(element: Element, pinData: Pin): void {
  // Remove any existing marker
  unmarkElement(element);

  // Mark the element
  element.classList.add("talknician-marked-element");

  // Create marker
  const marker = document.createElement("div");
  marker.className = "talknician-marker";
  marker.setAttribute("data-description", pinData.description);
  if (pinData.selector) {
    marker.setAttribute("data-selector", pinData.selector);
  }
  marker.title = pinData.description || "Click to edit";

  // Add marker to the element
  element.appendChild(marker);

  // Add click handler to marker
  marker.addEventListener("click", (e) => {
    e.stopPropagation();
    e.preventDefault();

    // Check if we're in pin mode
    storage.get<boolean>("talknician_pinning_active").then((isActive) => {
      if (isActive) {
        // Edit the pin
        const rect = marker.getBoundingClientRect();
        createDescriptionPopup(
          rect.top,
          rect.left,
          pinData.description,
          async (newDescription) => {
            if (newDescription.trim() === "") {
              // Remove pin if description is empty
              unmarkElement(element);
              await removePin(pinData);
            } else {
              // Update pin
              const updatedPin = {
                ...pinData,
                description: newDescription,
              };
              markElement(element, updatedPin);
              const savedPin = await savePin(updatedPin);

              // Open pin details window with explicit timeout
              console.log("Pin updated, opening details window");
              setTimeout(() => {
                openPinDetailsWindow(savedPin);
              }, 100);
            }
          }
        );
      } else {
        // If not in pin mode, just open pin details
        openPinDetailsWindow(pinData);
      }
    });
  });
}

// Create a floating coordinate-based marker
function createCoordinateMarker(pinData: Pin): void {
  if (!pinData.xPercent || !pinData.yPercent) return;

  // Calculate absolute position in document coordinates
  const { x, y } = getAbsolutePosition(pinData.xPercent, pinData.yPercent);

  // Create the floating marker container
  const markerContainer =
    document.getElementById("talknician-coordinate-markers") ||
    createCoordinateMarkerContainer();

  // Check if marker already exists at this position
  const existingMarker = document.querySelector(
    `.talknician-floating-marker[data-x-percent="${pinData.xPercent}"][data-y-percent="${pinData.yPercent}"]`
  );

  if (existingMarker) {
    existingMarker.remove();
  }

  // Create new marker
  const marker = document.createElement("div");
  marker.className = "talknician-floating-marker";
  marker.setAttribute("data-description", pinData.description);
  marker.setAttribute("data-x-percent", pinData.xPercent.toString());
  marker.setAttribute("data-y-percent", pinData.yPercent.toString());
  marker.title = pinData.description || "Click to edit";

  // Position the marker using absolute positioning instead of fixed
  marker.style.position = "absolute";
  marker.style.left = `${x}px`;
  marker.style.top = `${y}px`;

  // Add click handler to marker
  marker.addEventListener("click", (e) => {
    e.stopPropagation();
    e.preventDefault();

    // Check if we're in pin mode
    storage.get<boolean>("talknician_pinning_active").then((isActive) => {
      if (isActive) {
        // Edit the pin
        createDescriptionPopup(
          e.clientY, // Use viewport coordinates for popup
          e.clientX, // Use viewport coordinates for popup
          pinData.description,
          async (newDescription) => {
            if (newDescription.trim() === "") {
              // Remove pin if description is empty
              marker.remove();
              await removePin(pinData);
            } else {
              // Update pin
              const updatedPin = {
                ...pinData,
                description: newDescription,
              };
              marker.setAttribute("data-description", newDescription);
              marker.title = newDescription;
              await savePin(updatedPin);

              // Open pin details window with explicit timeout
              console.log("Coordinate pin updated, opening details window");
              setTimeout(() => {
                openPinDetailsWindow(updatedPin);
              }, 100);
            }
          }
        );
      } else {
        // If not in pin mode, just open pin details
        openPinDetailsWindow(pinData);
      }
    });
  });

  // Add to container
  markerContainer.appendChild(marker);
}

// Create or get the coordinate marker container
function createCoordinateMarkerContainer(): HTMLElement {
  let container = document.getElementById("talknician-coordinate-markers");

  if (!container) {
    container = document.createElement("div");
    container.id = "talknician-coordinate-markers";
    // Use absolute positioning instead of fixed
    container.style.position = "absolute";
    container.style.top = "0";
    container.style.left = "0";
    container.style.width = "100%";
    container.style.height = "100%";
    container.style.pointerEvents = "none";
    container.style.zIndex = "9997";
    document.body.appendChild(container);
  }

  return container;
}

// Remove marker from an element
function unmarkElement(element: Element): void {
  element.classList.remove("talknician-marked-element");
  const marker = element.querySelector(".talknician-marker");
  if (marker) {
    element.removeChild(marker);
  }
}

// Update positions of coordinate-based markers when document size changes
function updateCoordinateMarkers(): void {
  const markers = document.querySelectorAll(".talknician-floating-marker");

  markers.forEach((marker) => {
    const xPercent = parseFloat(marker.getAttribute("data-x-percent") || "0");
    const yPercent = parseFloat(marker.getAttribute("data-y-percent") || "0");

    const { x, y } = getAbsolutePosition(xPercent, yPercent);

    (marker as HTMLElement).style.left = `${x}px`;
    (marker as HTMLElement).style.top = `${y}px`;
  });
}

// Function to open pin details in a new window
function openPinDetailsWindow(pinData: Pin): void {
  console.log("Opening pin details window for pin:", pinData.description);

  // Convert pin data to a safe URL parameter
  // The screenshot is now a URL, so we can include it directly
  const pinDataString = encodeURIComponent(JSON.stringify(pinData));

  // Store timestamp for reliable identification
  const pinId = pinData.timestamp.toString();

  // Send message to background script to open window
  chrome.runtime.sendMessage(
    {
      action: "openPinDetailsWindow",
      pinData: pinDataString,
      url: window.location.href,
      pageTitle: document.title,
      pinId: pinId,
    },
    (response) => {
      if (chrome.runtime.lastError) {
        console.error(
          "Error opening pin details window:",
          chrome.runtime.lastError
        );
      } else if (response && response.success) {
        console.log("Pin details window opened with ID:", response.windowId);
      }
    }
  );
}

// Add the necessary styles
function addStyles(): void {
  // Check if styles are already added
  if (document.getElementById("talknician-pin-styles")) {
    return;
  }

  const styleElement = document.createElement("style");
  styleElement.id = "talknician-pin-styles";
  styleElement.textContent = `
    .talknician-marked-element {
      position: relative !important;
    }

    .talknician-marker,
    .talknician-floating-marker {
      width: 25px;
      height: 25px;
      background-color:hsl(183, 100.00%, 62.50%, 0.5);
      border-radius: 2rem;
      cursor: pointer;
      z-index: 9999;
      opacity: 0.8;
      transition: all 0.2s ease;
      pointer-events: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url("${iconBase64}");
      background-size: 18px;
      background-position: center;
      background-repeat: no-repeat;
    }

    .talknician-marker {
      position: absolute;
      top: 0;
      right: 0;
    }

    .talknician-floating-marker {
      position: absolute;
      transform: translate(-50%, -50%);
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    }

    .talknician-marker:hover,
    .talknician-floating-marker:hover {
      opacity: 1;
      transform: scale(1.2);
      background-color:hsl(183, 100.00%, 62.50%, 1);
    }

    .talknician-floating-marker:hover {
      transform: translate(-50%, -50%) scale(1.2);
    }

    .talknician-marker:hover::before,
    .talknician-floating-marker:hover::before {
      content: attr(data-description);
      position: absolute;
      top: 30px;
      right: 0;
      background-color: #1a1a1a;
      color: #fff;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      min-width: 100px;
      max-width: 200px;
      word-wrap: break-word;
      z-index: 9999;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }

    #talknician-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: transparent;
      cursor: crosshair;
      z-index: 9998;
    }
  `;

  document.head.appendChild(styleElement);
}

// Create overlay for click interception
function createOverlay(show: boolean): HTMLElement {
  let overlay = document.getElementById("talknician-overlay");

  if (!overlay) {
    overlay = document.createElement("div");
    overlay.id = "talknician-overlay";

    // Add click handler to overlay
    overlay.addEventListener("click", async (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Get element under click
      overlay.style.pointerEvents = "none";
      const elementUnderCursor = document.elementFromPoint(
        e.clientX,
        e.clientY
      );
      overlay.style.pointerEvents = "auto";

      if (elementUnderCursor && elementUnderCursor !== overlay) {
        // Determine if this is a large element that should use coordinate-based pinning
        const isLarge = isLargeElement(elementUnderCursor);

        // Show description popup
        createDescriptionPopup(
          e.clientY,
          e.clientX,
          null,
          async (description) => {
            if (description.trim() !== "") {
              let pinData: Pin;

              if (isLarge) {
                // For large elements, use coordinate-based pinning
                // Calculate pageX/pageY by adding scroll offset to clientX/clientY
                const pageX = e.clientX + window.scrollX;
                const pageY = e.clientY + window.scrollY;

                const relativePos = calculateRelativePosition(pageX, pageY);

                pinData = {
                  xPercent: relativePos.xPercent,
                  yPercent: relativePos.yPercent,
                  description: description.trim(),
                  timestamp: Date.now(),
                  url: window.location.href,
                  isCoordinateBased: true,
                };
                createCoordinateMarker(pinData);
              } else {
                // For smaller elements, use element-based pinning
                const selector = getUniqueSelector(elementUnderCursor);
                pinData = {
                  selector,
                  description: description.trim(),
                  timestamp: Date.now(),
                  url: window.location.href,
                  isCoordinateBased: false,
                };
                markElement(elementUnderCursor, pinData);
              }

              try {
                // Save the pin data first
                const savedPin = await savePin(pinData);
                console.log(
                  "Pin saved successfully, now opening details window"
                );
                console.log("Saved pin data:", savedPin);

                // Then explicitly open pin details window after creating the pin
                setTimeout(() => {
                  openPinDetailsWindow(savedPin);
                }, 100);
              } catch (error) {
                console.error("Error saving pin:", error);
              }
            }
          }
        );
      }
    });

    document.body.appendChild(overlay);
  }

  // Show or hide
  overlay.style.display = show ? "block" : "none";
  return overlay;
}

// Toggle pin mode
function togglePinMode(active: boolean): void {
  createOverlay(active);

  // Update storage
  storage.set("talknician_pinning_active", active);

  // Update cursor
  document.body.style.cursor = active ? "crosshair" : "";

  // Notify toolbar of state change
  if (!active) {
    chrome.runtime.sendMessage({
      action: "PIN_CREATED",
      active: false,
    });
  }
}

// Toggle pins visibility
function togglePinsVisibility(hidden: boolean): void {
  console.log("Toggling pins visibility:", hidden);

  // Add CSS for hidden pins if it doesn't exist
  let styleElement = document.getElementById(
    "talknician-pin-visibility-styles"
  );
  if (!styleElement) {
    styleElement = document.createElement("style");
    styleElement.id = "talknician-pin-visibility-styles";
    styleElement.textContent = `
      .talknician-pin-hidden {
        display: none !important;
      }
    `;
    document.head.appendChild(styleElement);
  }

  // Select all pin markers (both element-based and coordinate-based)
  const elementMarkers = document.querySelectorAll(".talknician-marker");
  const coordinateMarkers = document.querySelectorAll(
    ".talknician-floating-marker"
  );

  // Toggle visibility for element-based markers
  elementMarkers.forEach((marker) => {
    if (hidden) {
      marker.classList.add("talknician-pin-hidden");
    } else {
      marker.classList.remove("talknician-pin-hidden");
    }
  });

  // Toggle visibility for coordinate-based markers
  coordinateMarkers.forEach((marker) => {
    if (hidden) {
      marker.classList.add("talknician-pin-hidden");
    } else {
      marker.classList.remove("talknician-pin-hidden");
    }
  });

  // Update storage
  storage.set("talknician_pins_hidden", hidden);
}

// Initialize pinning
function initializePinning(): void {
  console.log("Waiting for page to fully load before initializing pins...");

  // Delay the initialization to avoid React hydration errors
  const startPinningFeature = () => {
    // Add styles
    addStyles();

    // Create container for coordinate-based markers
    createCoordinateMarkerContainer();

    // Load existing markers
    loadMarkers();

    // Setup window resize handler for coordinate-based markers
    window.addEventListener("resize", debounce(updateCoordinateMarkers, 100));

    // Setup URL change detection for single-page applications
    setupUrlChangeDetection();

    // Listen for messages
    chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
      if (request.action === "TOGGLE_PINNING_MODE") {
        togglePinMode(request.active);
        sendResponse({ success: true });
      } else if (request.action === "TOGGLE_PINS_VISIBILITY") {
        togglePinsVisibility(request.hidden);
        sendResponse({ success: true });
      } else if (request.action === "pinDeleted") {
        // Handle pin deletion notification
        console.log("Received pin deletion notification", request);
        // Refresh pins to update the UI
        loadMarkers();
        sendResponse({ success: true });
      } else if (request.action === "pinUpdated") {
        // Handle pin update notification
        console.log("Received pin update notification", request);
        // Refresh pins to update the UI
        loadMarkers();
        sendResponse({ success: true });
      }
    });

    // Listen for custom pin deleted event
    window.addEventListener("talknician_pin_deleted", (event: Event) => {
      console.log(
        "Received custom pin deleted event",
        (event as CustomEvent).detail
      );
      // Refresh pins to update the UI
      loadMarkers();
    });

    // Listen for custom pin updated event
    window.addEventListener("talknician_pin_updated", (event: Event) => {
      console.log(
        "Received custom pin updated event",
        (event as CustomEvent).detail
      );
      // Refresh pins to update the UI
      loadMarkers();
    });

    // Check initial pinning state
    storage.get<boolean>("talknician_pinning_active").then((isActive) => {
      if (isActive) {
        togglePinMode(true);
      }
    });

    // Check initial pin visibility state
    storage.get<boolean>("talknician_pins_hidden").then((isHidden) => {
      if (isHidden) {
        togglePinsVisibility(true);
      }
    });
  };

  // Function to check if DOM has stabilized (for React/Next.js hydration)
  const checkDomStabilization = () => {
    // Create a mutation observer to detect when DOM changes settle
    let mutationCount = 0;
    let lastMutationTime = Date.now();

    const observer = new MutationObserver(() => {
      mutationCount++;
      lastMutationTime = Date.now();
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true,
    });

    // Check if DOM has stabilized every 500ms
    const checkInterval = setInterval(() => {
      const timeSinceLastMutation = Date.now() - lastMutationTime;

      // If no mutations for 1 second, assume hydration is complete
      if (timeSinceLastMutation > 1000) {
        clearInterval(checkInterval);
        observer.disconnect();
        startPinningFeature();
      }
    }, 500);

    // Fallback timeout - initialize after 5 seconds regardless
    setTimeout(() => {
      clearInterval(checkInterval);
      observer.disconnect();
      startPinningFeature();
    }, 5000);
  };

  if (document.readyState === "complete") {
    // If document is already loaded, wait a bit more for hydration
    setTimeout(checkDomStabilization, 500);
  } else {
    // Wait for page to load first
    window.addEventListener("load", () => {
      setTimeout(checkDomStabilization, 500);
    });
  }
}

// Clear all existing markers
function clearAllMarkers(): void {
  // Clear element-based markers
  const markedElements = document.querySelectorAll(
    ".talknician-marked-element"
  );
  markedElements.forEach((element) => {
    unmarkElement(element as Element);
  });

  // Clear coordinate-based markers
  const markerContainer = document.getElementById(
    "talknician-coordinate-markers"
  );
  if (markerContainer) {
    markerContainer.innerHTML = "";
  }
}

// Load saved markers
async function loadMarkers(): Promise<void> {
  // Clear existing markers first
  clearAllMarkers();

  // Fetch pins from API
  const pins = await getPinsForCurrentPage();

  pins.forEach((pin) => {
    try {
      if (pin.isCoordinateBased) {
        // For coordinate-based pins
        createCoordinateMarker(pin);
      } else {
        // For element-based pins
        const element = document.querySelector(pin.selector);
        if (element) {
          markElement(element, pin);
        }
      }
    } catch (error) {
      console.error("Error loading marker:", error, pin);
    }
  });
}

// Setup URL change detection for single-page applications
function setupUrlChangeDetection(): void {
  let currentUrl = window.location.href;

  // Store the current pathname and hash for comparison
  let lastPathname = window.location.pathname;
  let lastHash = window.location.hash;

  // Function to check if the URL has changed in a meaningful way
  const hasUrlChanged = () => {
    const currentPathname = window.location.pathname;
    const currentHash = window.location.hash;

    // Check if pathname or hash has changed
    const pathnameChanged = currentPathname !== lastPathname;
    const hashChanged = currentHash !== lastHash;

    // Update stored values
    lastPathname = currentPathname;
    lastHash = currentHash;

    return pathnameChanged || hashChanged;
  };

  // Method 1: Use MutationObserver to detect DOM changes that might indicate a route change
  const observer = new MutationObserver(
    debounce(() => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        if (hasUrlChanged()) {
          console.log("URL changed to:", currentUrl);
          loadMarkers(); // Reload pins for the new URL
        }
      }
    }, 300)
  );

  // Observe changes to the document body
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  // Method 2: Monitor history state changes (pushState/replaceState)
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  // Override pushState
  history.pushState = function (...args) {
    originalPushState.apply(this, args);
    if (hasUrlChanged()) {
      console.log("pushState detected, URL changed to:", window.location.href);
      loadMarkers(); // Reload pins for the new URL
    }
  };

  // Override replaceState
  history.replaceState = function (...args) {
    originalReplaceState.apply(this, args);
    if (hasUrlChanged()) {
      console.log(
        "replaceState detected, URL changed to:",
        window.location.href
      );
      loadMarkers(); // Reload pins for the new URL
    }
  };

  // Method 3: Listen for popstate events (browser back/forward)
  window.addEventListener("popstate", () => {
    if (hasUrlChanged()) {
      console.log("popstate detected, URL changed to:", window.location.href);
      loadMarkers(); // Reload pins for the new URL
    }
  });

  // Method 4: Listen for hashchange events
  window.addEventListener("hashchange", () => {
    console.log("hashchange detected, URL changed to:", window.location.href);
    loadMarkers(); // Reload pins for the new URL
  });
}

// Start initialization
initializePinning();
