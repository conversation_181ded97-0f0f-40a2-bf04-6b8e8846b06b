import React, { useEffect, useState } from "react";
import styled, { keyframes } from "styled-components";

const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`;

const OverlayContainer = styled.div<{ isVisible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  opacity: ${(props) => (props.isVisible ? 1 : 0)};
  animation: ${fadeOut} 0.6s ease-out;
  animation-delay: 0.6s;
  pointer-events: none;
`;

const Message = styled.div`
  color: white;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  padding: 20px 40px;
  border-radius: 8px;
`;

interface CaptureOverlayProps {
  onAnimationComplete?: () => void;
}

export const CaptureStartOverlay: React.FC<CaptureOverlayProps> = ({
  onAnimationComplete,
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Start fade out after 1 second
    const fadeTimeout = setTimeout(() => {
      setIsVisible(false);
    }, 1000);

    // Remove component after animation completes
    const removeTimeout = setTimeout(() => {
      if (onAnimationComplete) {
        onAnimationComplete();
      }
    }, 1600); // 1s delay + 0.6s animation

    return () => {
      clearTimeout(fadeTimeout);
      clearTimeout(removeTimeout);
    };
  }, []);

  return (
    <OverlayContainer isVisible={isVisible}>
      <Message>Capture has started!</Message>
    </OverlayContainer>
  );
};

export default function StartCaptureOverlay() {
  return <div></div>;
}
